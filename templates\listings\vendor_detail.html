{% extends "base.html" %}
{% load static %}

{% block title %}{{ vendor.name }} - TasteLocal Vendor{% endblock %}

{% block content %}
<div class="container mt-4">

    {# Optional Breadcrumbs #}
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="{% url 'listings:home' %}">Home</a></li>
          <li class="breadcrumb-item"><a href="{% url 'listings:listing_list' %}">Explore</a></li>
          {# Maybe add link to a vendor list page if you create one #}
          <li class="breadcrumb-item active" aria-current="page">{{ vendor.name }}</li>
        </ol>
    </nav>

    <!-- Vendor Details Section -->
    <div class="card shadow-sm mb-5">
        <div class="card-header bg-light d-flex justify-content-between align-items-center"> {# <-- ADD FLEX CLASSES HERE #}
             <h1 class="card-title mb-0">{{ vendor.name }}</h1>
             {# --- FAVORITE VENDOR BUTTON (NO CHANGE TO THE BUTTON CODE ITSELF NEEDED) --- #}
             <div id="favorite-vendor-button-container"> {# Optional: Add a container if you plan AJAX for this too #}
                {% if user.is_authenticated %}
                    <form method="post" action="{% url 'listings:toggle_favorite_vendor' vendor_pk=vendor.pk %}" id="favorite-vendor-form" class="d-inline"> {# Added id for potential JS #}
                        {% csrf_token %}
                        {% if is_favorited_vendor %}
                            <button type="submit" class="btn btn-danger btn-sm" title="Remove from Favorite Vendors">
                                <i class="fas fa-heart-broken me-1"></i> Unfavorite Vendor
                            </button>
                        {% else %}
                            <button type="submit" class="btn btn-outline-danger btn-sm" title="Add to Favorite Vendors">
                                <i class="fas fa-heart me-1"></i> Favorite Vendor
                            </button>
                        {% endif %}
                    </form>
                {% else %}
                    <a href="{% url 'login' %}?next={{ request.path }}" class="btn btn-outline-danger btn-sm" title="Log in to Favorite Vendor">
                        <i class="fas fa-heart me-1"></i> Favorite Vendor
                    </a>
                {% endif %}
            </div>
             {# --- END FAVORITE VENDOR BUTTON --- #}
        </div>
        <div class="card-body">
            <div class="row">
                {# Column for Logo (Optional Layout) #}
                {% if vendor.logo %}
                <div class="col-md-3 text-center mb-3 mb-md-0">
                     <img src="{{ vendor.logo.url }}" alt="{{ vendor.name }} Logo" class="img-fluid rounded shadow-sm" style="max-height: 150px;">
                </div>
                <div class="col-md-9"> {# Adjust column width if logo is present #}
                {% else %}
                <div class="col-md-12"> {# Full width if no logo #}
                {% endif %}
                    {# Existing Vendor Info #}
                    {% if vendor.description %}
                        <p class="lead">{{ vendor.description }}</p>
                        <hr>
                    {% endif %}
                    <div class="row">
                        <div class="col-lg-6"> {# Adjusted inner columns #}
                            <h5>Contact & Location</h5>
                            <ul class="list-unstyled">
                                {% if vendor.address %}
                                    <li><strong><i class="fas fa-map-marker-alt me-2"></i>Address:</strong> {{ vendor.address }}</li>
                                {% endif %}
                                {% if vendor.phone_number %}
                                    <li><strong><i class="fas fa-phone me-2"></i>Phone:</strong> {{ vendor.phone_number }}</li>
                                {% endif %}
                                {% if vendor.website %}
                                    <li><strong><i class="fas fa-globe me-2"></i>Website:</strong> <a href="{{ vendor.website }}" target="_blank" rel="noopener noreferrer">{{ vendor.website }}</a></li>
                                {% endif %}
                            </ul>
                        </div>
                        <div class="col-lg-6"> {# Adjusted inner columns #}
                            {% if vendor.opening_hours %}
                                <h5><i class="fas fa-clock me-2"></i>Opening Hours</h5>
                                <p style="white-space: pre-wrap;">{{ vendor.opening_hours|linebreaksbr }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div> {# End vendor info column #}
            </div> {# End main row inside card-body #}

            {# --- Google Map Integration --- #}
            {% if vendor.latitude and vendor.longitude %}
            <hr>
            <div class="row mt-3">
                <div class="col-12">
                    <h5><i class="fas fa-map-marked-alt me-2"></i>Location Map</h5>
                    {# This div will hold the map #}
                    <div id="map" style="height: 350px; width: 100%; border: 1px solid #ccc; margin-top: 10px;"></div>
                </div>
            </div>
            {% else %}
                {# Optional: Message if coordinates are missing #}
                {# <p class="text-muted mt-3"><small>Location map not available.</small></p> #}
            {% endif %}
            {# --- End Google Map Integration --- #}

        </div>
        <div class="card-footer text-muted">
            Vendor since: {{ vendor.created_at|date:"F Y" }}
        </div>
    </div>

    <!-- Listings by this Vendor Section -->
    <h2 class="mb-4">Offerings from {{ vendor.name }}</h2>

    {% if listings %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for listing in listings %}
            <div class="col">
                <div class="card h-100 shadow-sm">
                    {% if listing.image %}
                        <img src="{{ listing.image.url }}" class="card-img-top" alt="{{ listing.title }}" style="height: 220px; object-fit: cover;">
                    {% else %}
                         <img src="https://via.placeholder.com/400x220.png?text={{ vendor.name }}" class="card-img-top" alt="Placeholder Image" style="height: 220px; object-fit: cover;">
                    {% endif %}
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">{{ listing.title }}</h5>
                            {% if listing.id in favorited_listing_ids %}
                                <span class="badge bg-danger" title="Favorited"><i class="fas fa-heart"></i></span>
                            {% endif %}
                        </div>
                        <p class="card-text text-muted mb-2">
                            {% if listing.category %}<small>Category: {{ listing.category.name }}</small>{% endif %}
                        </p>
                        <p class="card-text flex-grow-1">{{ listing.description|truncatewords:20 }}</p>

                        {% if listing.average_rating %}
                            <p class="card-text mb-2"><small class="text-warning">★ {{ listing.average_rating|floatformat:1 }} / 5</small></p>
                        {% else %}
                            <p class="card-text mb-2"><small class="text-muted">No reviews yet</small></p>
                        {% endif %}

                        <a href="{% url 'listings:listing_detail' pk=listing.pk %}" class="btn btn-outline-primary mt-auto align-self-start">View Details</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info" role="alert">
            {{ vendor.name }} has no active listings on the platform currently.
        </div>
    {% endif %}

    <!-- User Query Form Section -->
    {% if user.is_authenticated %}
    <hr class="my-5">
    <div class="card shadow-sm mb-5">
        <div class="card-header bg-secondary text-white">
            <h3 class="mb-0"><i class="fas fa-question-circle me-2"></i>Contact {{ vendor.name }}</h3>
        </div>
        <div class="card-body">
            <p class="text-muted">Have a question for {{ vendor.name }}? Fill out the form below.</p>
            {# The view passes VendorQueryForm as 'query_form' to the context of VendorDetailView #}
            <form method="post" action="{% url 'listings:submit_vendor_query' vendor_pk=vendor.pk %}" novalidate>
                {% csrf_token %}

                {# Display Non-Field Errors if any (though this form is simple) #}
                {% if query_form.non_field_errors %}
                    <div class="alert alert-danger" role="alert">
                        {% for error in query_form.non_field_errors %}
                            <p class="mb-0">{{ error }}</p>
                        {% endfor %}
                    </div>
                {% endif %}

                {# Subject Field #}
                <div class="mb-3">
                    {% with field=query_form.subject %}
                        <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                        {{ field.as_widget }}
                        {% if field.errors %}
                            <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                        {% endif %}
                    {% endwith %}
                </div>

                {# Message Field #}
                <div class="mb-3">
                     {% with field=query_form.message %}
                        <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                        {{ field.as_widget }}
                        {% if field.errors %}
                            <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                        {% endif %}
                    {% endwith %}
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-1"></i> Send Query
                </button>
            </form>
        </div>
    </div>
    {% else %}
    <hr class="my-5">
    <div class="text-center p-4 border rounded bg-light">
        <p class="mb-2">Want to ask {{ vendor.name }} a question?</p>
        <a href="{% url 'login' %}?next={{ request.path }}" class="btn btn-primary">
            <i class="fas fa-sign-in-alt me-1"></i> Log in to Send a Query
        </a>
    </div>
    {% endif %}
    <!-- End User Query Form Section -->

</div> {# End container #}

{# Font Awesome for Icons (Optional - Add to base.html if used widely) #}
{% block extra_head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
{% endblock %}
{# --- Add Google Maps Script Block --- #}
{% block extra_scripts %}
    {# Only include map scripts if coordinates exist #}
    {% if vendor.latitude and vendor.longitude %}
    <script>
        function initMap() {
            // Convert Django template variables (Decimal) to JavaScript numbers
            // Use |stringformat:'f' to prevent potential scientific notation issues
            const lat = parseFloat("{{ vendor.latitude|stringformat:'f' }}");
            const lng = parseFloat("{{ vendor.longitude|stringformat:'f' }}");

            // Basic check if coordinates are valid numbers
            if (isNaN(lat) || isNaN(lng)) {
                console.error("Invalid latitude or longitude values provided for the map.");
                document.getElementById('map').innerHTML = '<p class="text-danger text-center p-3">Error loading map: Invalid coordinates.</p>';
                return;
            }

            // The location of the vendor
            const vendorLocation = { lat: lat, lng: lng };

            // The map, centered at vendorLocation
            const map = new google.maps.Map(document.getElementById("map"), {
                zoom: 15, // Adjust zoom level as desired (higher number = more zoomed in)
                center: vendorLocation,
                mapTypeId: 'roadmap' // Other options: 'satellite', 'hybrid', 'terrain'
            });

            // The marker, positioned at vendorLocation
            const marker = new google.maps.Marker({
                position: vendorLocation,
                map: map,
                title: "{{ vendor.name|escapejs }}" // Add vendor name as marker title (escape JS)
            });
        }
    </script>

    {# Load the Google Maps JavaScript API #}
    {# IMPORTANT: Replace YOUR_API_KEY with your actual key #}
    {# In production, load the key securely (e.g., from Django settings/environment variable) #}
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyC-Gm9OW4FjRnJ_bdZijQoMODY2QUgRyec&callback=initMap">
    </script>
    {% endif %}
{% endblock %}
{# --- End Google Maps Script Block --- #}
{% endblock %}
