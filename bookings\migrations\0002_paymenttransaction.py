# Generated by Django 5.1.6 on 2025-05-07 14:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bookings', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('card_number_dummy', models.Char<PERSON>ield(help_text='Dummy card number', max_length=20)),
                ('expiry_date_dummy', models.Char<PERSON>ield(help_text='Dummy expiry MM/YY', max_length=7)),
                ('cvc_dummy', models.Char<PERSON>ield(help_text='Dummy CVC', max_length=4)),
                ('amount_paid', models.DecimalField(decimal_places=2, max_digits=10)),
                ('transaction_timestamp', models.DateTimeField(auto_now_add=True)),
                ('payment_status', models.Char<PERSON>ield(default='simulated_success', max_length=20)),
                ('booking', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payment_transaction', to='bookings.booking')),
            ],
            options={
                'ordering': ['-transaction_timestamp'],
            },
        ),
    ]
