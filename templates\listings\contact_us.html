{% extends "base.html" %}
{% load static %}

{% block title %}Contact Us - TasteLocal{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <h1 class="text-center mb-4">Get In Touch</h1>
    <p class="lead text-center text-muted mb-5">
        Have questions, feedback, or want to partner with us? We'd love to hear from you!
    </p>

    {# Display success/error messages from form submission #}
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row g-4">
        <!-- Contact Information Column -->
        <div class="col-lg-5">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-secondary text-white">
                    <h4 class="mb-0"><i class="fas fa-address-book me-2"></i>Contact Information</h4>
                </div>
                <div class="card-body">
                    <p>Reach out to us through any of the following methods:</p>
                    <ul class="list-unstyled">
                        <li class="mb-3">
                            <strong class="d-block"><i class="fas fa-envelope fa-fw me-2 text-primary"></i>Email:</strong>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li class="mb-3">
                            <strong class="d-block"><i class="fas fa-phone fa-fw me-2 text-primary"></i>Phone:</strong>
                            <a href="tel:+1234567890">+1 (234) 567-890</a> (Mon-Fri, 9am-5pm)
                        </li>
                        <li class="mb-3">
                            <strong class="d-block"><i class="fas fa-map-marker-alt fa-fw me-2 text-primary"></i>Address:</strong>
                            123 Culinary Lane<br>
                            Foodie City, FC 98765<br>
                            RegionName
                            {# Add address only if applicable #}
                        </li>
                    </ul>
                    <hr>
                    <p class="text-muted small">For vendor-specific inquiries, please use the vendor dashboard or contact vendor support directly if available.</p>
                </div>
            </div>
        </div>

        <!-- Contact Form Column -->
        <div class="col-lg-7">
            <div class="card shadow-sm h-100">
                 <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="fas fa-paper-plane me-2"></i>Send Us a Message</h4>
                </div>
                <div class="card-body">
                    {# Assume the view passes a 'form' object of type ContactForm #}
                    {# Replace 'contact' with the actual name of your contact URL pattern #}
                    <form method="post" action="{% url 'contact' %}" novalidate>
                        {% csrf_token %}

                        {# Display Non-Field Errors #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        {# Render form fields explicitly for better control #}
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {% with field=form.name %}
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    <input type="text" name="{{ field.name }}" id="{{ field.id_for_label }}"
                                           class="form-control {% if field.errors %}is-invalid{% endif %}"
                                           value="{{ field.value|default:'' }}" required>
                                    {% if field.errors %}
                                        <div class="invalid-feedback">{{ field.errors|striptags }}</div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                            <div class="col-md-6 mb-3">
                                {% with field=form.email %}
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    <input type="email" name="{{ field.name }}" id="{{ field.id_for_label }}"
                                           class="form-control {% if field.errors %}is-invalid{% endif %}"
                                           value="{{ field.value|default:'' }}" required>
                                    {% if field.errors %}
                                        <div class="invalid-feedback">{{ field.errors|striptags }}</div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                        </div>

                        <div class="mb-3">
                             {% with field=form.subject %}
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                <input type="text" name="{{ field.name }}" id="{{ field.id_for_label }}"
                                       class="form-control {% if field.errors %}is-invalid{% endif %}"
                                       value="{{ field.value|default:'' }}" required>
                                {% if field.errors %}
                                    <div class="invalid-feedback">{{ field.errors|striptags }}</div>
                                {% endif %}
                            {% endwith %}
                        </div>

                        <div class="mb-3">
                             {% with field=form.message %}
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                <textarea name="{{ field.name }}" id="{{ field.id_for_label }}"
                                          class="form-control {% if field.errors %}is-invalid{% endif %}"
                                          rows="5" required>{{ field.value|default:'' }}</textarea>
                                {% if field.errors %}
                                    <div class="invalid-feedback">{{ field.errors|striptags }}</div>
                                {% endif %}
                            {% endwith %}
                        </div>

                        <div class="d-grid"> {# Full width button #}
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>Send Message
                            </button>
                        </div>
                    </form>
                </div> {# End card-body #}
            </div> {# End card #}
        </div> {# End Form Column #}
    </div> {# End row #}
</div> {# End container #}
{% endblock %}
