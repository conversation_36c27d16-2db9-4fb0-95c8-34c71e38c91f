# Generated by Django 5.1.6 on 2025-05-08 13:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('listings', '0002_vendor_logo'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='VendorQuery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('reply', models.TextField(blank=True, help_text="Vendor's reply to the query", null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('replied_at', models.DateTimeField(blank=True, null=True)),
                ('is_read_by_vendor', models.BooleanField(default=False, help_text='Has the vendor seen this query?')),
                ('is_replied', models.<PERSON>oleanField(default=False)),
                ('user', models.ForeignKey(help_text='User who sent the query', on_delete=django.db.models.deletion.CASCADE, related_name='sent_queries', to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='queries', to='listings.vendor')),
            ],
            options={
                'verbose_name': 'Vendor Query',
                'verbose_name_plural': 'Vendor Queries',
                'ordering': ['-created_at'],
            },
        ),
    ]
