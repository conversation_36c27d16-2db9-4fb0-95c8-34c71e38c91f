# listings/admin.py
from django.contrib import admin
from .models import Category, Vendor, Listing, Review, VendorQuery # <-- Add VendorQuery
from bookings.models import Booking # Import Booking
from django.db.models import Count, Avg # Import for aggregation
from django.urls import reverse
from django.utils.html import format_html

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'listing_count') # Added listing_count
    search_fields = ('name',)
    prepopulated_fields = {'slug': ('name',)}

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            _listing_count=Count('listings', distinct=True)
        )
        return queryset

    def listing_count(self, obj):
        return obj._listing_count
    listing_count.admin_order_field = '_listing_count'
    listing_count.short_description = 'Listings'


@admin.register(Vendor)
class VendorAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner_link', 'address', 'is_approved', 'listing_count_for_vendor', 'total_bookings_for_vendor', 'created_at')
    list_filter = ('is_approved', 'created_at')
    search_fields = ('name', 'description', 'owner__username')
    actions = ['approve_vendors']
    readonly_fields = ('owner',) # Make owner read-only if set on creation

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            _listing_count=Count('listings', distinct=True),
            _total_bookings=Count('listings__bookings', distinct=True) # Count bookings related to listings of this vendor
        )
        return queryset

    def owner_link(self, obj):
        if obj.owner:
            link = reverse("admin:auth_user_change", args=[obj.owner.id])
            return format_html('<a href="{}">{}</a>', link, obj.owner.username)
        return "-"
    owner_link.short_description = 'Owner'
    owner_link.admin_order_field = 'owner__username'


    def listing_count_for_vendor(self, obj):
        return obj._listing_count
    listing_count_for_vendor.admin_order_field = '_listing_count'
    listing_count_for_vendor.short_description = 'Listings'

    def total_bookings_for_vendor(self, obj):
        return obj._total_bookings
    total_bookings_for_vendor.admin_order_field = '_total_bookings'
    total_bookings_for_vendor.short_description = 'Total Bookings'

    def approve_vendors(self, request, queryset):
        queryset.update(is_approved=True)
    approve_vendors.short_description = "Mark selected vendors as approved"

@admin.register(Listing)
class ListingAdmin(admin.ModelAdmin):
    list_display = ('title', 'vendor_link', 'category', 'listing_type', 'price', 'is_active', 'is_bookable', 'average_rating_display', 'booking_count_for_listing')
    list_filter = ('is_active', 'is_bookable', 'listing_type', 'category', 'vendor__is_approved')
    search_fields = ('title', 'description', 'vendor__name')
    readonly_fields = ('average_rating_display',) # Use the display method

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            _booking_count=Count('bookings', distinct=True),
            _avg_rating=Avg('reviews__rating')
        )
        return queryset

    def vendor_link(self, obj):
        if obj.vendor:
            link = reverse("admin:listings_vendor_change", args=[obj.vendor.id])
            return format_html('<a href="{}">{}</a>', link, obj.vendor.name)
        return "-"
    vendor_link.short_description = 'Vendor'
    vendor_link.admin_order_field = 'vendor__name'

    def booking_count_for_listing(self, obj):
        return obj._booking_count
    booking_count_for_listing.admin_order_field = '_booking_count'
    booking_count_for_listing.short_description = 'Bookings'

    def average_rating_display(self, obj):
        # Uses the property from the model if available, or the annotation
        if hasattr(obj, 'average_rating') and obj.average_rating is not None:
             return f"{obj.average_rating:.1f} / 5"
        elif obj._avg_rating is not None:
            return f"{obj._avg_rating:.1f} / 5"
        return "N/A"
    average_rating_display.admin_order_field = '_avg_rating'
    average_rating_display.short_description = 'Avg. Rating'


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('listing_link', 'user_link', 'rating', 'created_at_display')
    list_filter = ('rating', 'created_at')
    search_fields = ('comment', 'user__username', 'listing__title')
    readonly_fields = ('listing', 'user', 'rating', 'comment', 'created_at') # Make reviews read-only in admin

    def listing_link(self, obj):
        link = reverse("admin:listings_listing_change", args=[obj.listing.id])
        return format_html('<a href="{}">{}</a>', link, obj.listing.title)
    listing_link.short_description = 'Listing'

    def user_link(self, obj):
        link = reverse("admin:auth_user_change", args=[obj.user.id])
        return format_html('<a href="{}">{}</a>', link, obj.user.username)
    user_link.short_description = 'User'

    def created_at_display(self, obj):
        return obj.created_at.strftime("%Y-%m-%d %H:%M")
    created_at_display.short_description = 'Created At'
    created_at_display.admin_order_field = 'created_at'

    def has_add_permission(self, request): # Reviews should come from users
        return False
    def has_change_permission(self, request, obj=None): # Generally don't edit reviews in admin
        return False
    
@admin.register(VendorQuery)
class VendorQueryAdmin(admin.ModelAdmin):
    list_display = ('subject', 'vendor_link', 'user_link', 'created_at', 'is_replied', 'is_read_by_vendor')
    list_filter = ('is_replied', 'is_read_by_vendor', 'created_at', 'vendor')
    search_fields = ('subject', 'message', 'reply', 'user__username', 'vendor__name')
    readonly_fields = ('vendor', 'user', 'subject', 'message', 'created_at', 'replied_at')
    list_editable = ('is_read_by_vendor',)
    fields = ('vendor', 'user', 'subject', 'message', 'created_at', 'is_read_by_vendor', 'reply', 'is_replied', 'replied_at')

    def vendor_link(self, obj):
        link = reverse("admin:listings_vendor_change", args=[obj.vendor.id])
        return format_html('<a href="{}">{}</a>', link, obj.vendor.name)
    vendor_link.short_description = 'Vendor'

    def user_link(self, obj):
        link = reverse("admin:auth_user_change", args=[obj.user.id])
        return format_html('<a href="{}">{}</a>', link, obj.user.username)
    user_link.short_description = 'User'

    # Customize the form in admin to allow replying if desired
    fields = ('vendor', 'user', 'subject', 'message', 'created_at', 'is_read_by_vendor', 'reply', 'is_replied', 'replied_at')
