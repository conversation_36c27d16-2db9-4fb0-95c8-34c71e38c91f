{% extends "base.html" %}
{% load static %} {# Load static tag if you plan to use project-level static files directly here #}

{% block title %}Welcome to TasteLocal - Discover Local Food{% endblock %}

{% block content %}

<div class="container py-5 text-center">
    <h1 class="display-5 fw-bold">Discover Authentic Local Flavors</h1>
    <p class="fs-4 text-muted">Explore unique culinary experiences, from street food tours to fine dining, recommended by locals.</p>
    <a href="{% url 'listings:listing_list' %}" class="btn btn-primary btn-lg mt-3">Start Exploring</a>
    {% if not user.is_authenticated %}
        <a href="{% url 'users:register' %}" class="btn btn-outline-secondary btn-lg mt-3">Join Us</a>
    {% endif %}
</div>

<hr class="my-5">

<!-- Featured Listings Section -->
<div class="container mb-5">
    <h2 class="text-center mb-4">Featured Experiences</h2>
    {% if featured_listings %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for listing in featured_listings %}
            <div class="col">
                <div class="card h-100 shadow-sm">
                    {% if listing.image %}
                        <img src="{{ listing.image.url }}" class="card-img-top" alt="{{ listing.title }}" style="height: 220px; object-fit: cover;">
                    {% else %}
                         {# Provide a default placeholder image #}
                         <img src="https://via.placeholder.com/400x220.png?text=TasteLocal+Experience" class="card-img-top" alt="Placeholder Image" style="height: 220px; object-fit: cover;">
                    {% endif %}
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ listing.title }}</h5>
                        <p class="card-text text-muted">
                            <small>By: <a href="{% url 'listings:vendor_detail' pk=listing.vendor.pk %}" class="text-decoration-none">{{ listing.vendor.name }}</a></small><br>
                            {% if listing.category %}<small>Category: {{ listing.category.name }}</small>{% endif %}
                        </p>
                        <p class="card-text">{{ listing.description|truncatewords:15 }}</p> {# Keep description short #}

                        {% if listing.average_rating %}
                            <p class="card-text"><small class="text-warning">★ {{ listing.average_rating|floatformat:1 }} / 5</small></p>
                        {% endif %}

                        <a href="{% url 'listings:listing_detail' pk=listing.pk %}" class="btn btn-outline-primary mt-auto">Learn More</a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center mt-4">
             <a href="{% url 'listings:listing_list' %}" class="btn btn-secondary">View All Listings</a>
        </div>
    {% else %}
        <p class="text-center text-muted">No featured listings available at the moment. Check back soon!</p>
    {% endif %}
</div>


<!-- Browse by Category Section -->
<div class="container py-5 bg-light rounded mb-5">
    <h2 class="text-center mb-4">Browse by Category</h2>
    {% if categories %}
        <div class="d-flex flex-wrap gap-2 justify-content-center">
            {% for category in categories %}
                <a href="{% url 'listings:listing_list' %}?category={{ category.slug }}" class="btn btn-outline-secondary btn-sm">{{ category.name }}</a>
            {% endfor %}
        </div>
    {% else %}
        <p class="text-center text-muted">No categories defined yet.</p>
    {% endif %}
</div>

<!-- Optional: Call to Action for Vendors -->
{% if user.is_authenticated and user.profile.user_type != 'vendor' or not user.is_authenticated %}
<div class="container text-center my-5">
    <hr>
    <h3 class="mt-5">Are you a Local Food Vendor?</h3>
    <p>Showcase your offerings to a wider audience of tourists and locals.</p>
    {% if user.is_authenticated %}
        {# Link to vendor dashboard or profile where they can register as vendor #}
        <a href="{% url 'users:profile' %}" class="btn btn-success">Become a Vendor Partner</a> {# Adjust URL if needed #}
    {% else %}
        <a href="{% url 'users:register' %}" class="btn btn-success">Register Your Business</a>
    {% endif %}
</div>
{% endif %}


{% endblock %}

{% block extra_scripts %}
{# Add any page-specific JavaScript here if needed #}
{% endblock %}
