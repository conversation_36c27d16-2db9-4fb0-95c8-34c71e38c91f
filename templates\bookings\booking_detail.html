{% extends "base.html" %}
{% load booking_extras %} 

{% load static %}

{% block title %}Booking Details - {{ booking.listing.title }}{% endblock %}

{% block content %}
<div class="container mt-4">

    {# Optional Breadcrumbs #}
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="{% url 'listings:home' %}">Home</a></li>
          {# Adjust breadcrumb if vendor is viewing #}
          {% if request.user.is_authenticated and request.user.profile.user_type == 'vendor' %}
            <li class="breadcrumb-item"><a href="{% url 'listings:vendor_dashboard' %}">Vendor Dashboard</a></li>
          {% else %}
            <li class="breadcrumb-item"><a href="{% url 'bookings:user_bookings' %}">My Bookings</a></li>
          {% endif %}
          <li class="breadcrumb-item active" aria-current="page">Booking #{{ booking.id }}</li>
        </ol>
    </nav>

    <h1 class="mb-4">Booking Details <small class="text-muted">#{{ booking.id }}</small></h1>

    {# Display messages if any actions were taken #}
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <a href="{% url 'listings:listing_detail' pk=booking.listing.pk %}" class="text-decoration-none">{{ booking.listing.title }}</a>
            </h5>
            <span class="badge rounded-pill fs-6
                {% if booking.status == 'confirmed' %} bg-success
                {% elif booking.status == 'pending' %} bg-warning text-dark
                {% elif booking.status == 'cancelled' %} bg-danger
                {% elif booking.status == 'completed' %} bg-secondary
                {% else %} bg-light text-dark {% endif %}">
                {{ booking.get_status_display }}
            </span>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <h5 class="mb-3">Booking Information</h5>
                    <dl class="row mb-0">
                        <dt class="col-sm-5">Listing:</dt>
                        <dd class="col-sm-7"><a href="{% url 'listings:listing_detail' pk=booking.listing.pk %}">{{ booking.listing.title }}</a></dd>

                        <dt class="col-sm-5">Vendor:</dt>
                        <dd class="col-sm-7"><a href="{% url 'listings:vendor_detail' pk=booking.listing.vendor.pk %}">{{ booking.listing.vendor.name }}</a></dd>

                        <dt class="col-sm-5">Booked By:</dt>
                        <dd class="col-sm-7">{{ booking.user.username }}
                            {% if booking.user.get_full_name %}({{ booking.user.get_full_name }}){% endif %}
                        </dd>

                        <dt class="col-sm-5">Booked Date & Time:</dt>
                        <dd class="col-sm-7">{{ booking.booking_date|date:"D, M j, Y, P" }}</dd>

                        <dt class="col-sm-5">Number of Guests:</dt>
                        <dd class="col-sm-7">{{ booking.number_of_people }}</dd>

                        {% if booking.total_price is not None %}
                        <dt class="col-sm-5">Total Price:</dt>
                        <dd class="col-sm-7">${{ booking.total_price|floatformat:2 }}</dd>
                        {% endif %}

                        <dt class="col-sm-5">Booking Placed:</dt>
                        <dd class="col-sm-7">{{ booking.created_at|date:"M j, Y, P" }}</dd>

                        {% if booking.created_at != booking.updated_at %}
                        <dt class="col-sm-5">Last Updated:</dt>
                        <dd class="col-sm-7">{{ booking.updated_at|date:"M j, Y, P" }}</dd>
                        {% endif %}
                    </dl>
                </div>
                <div class="col-md-6">
                    <h5 class="mb-3">Additional Details</h5>
                    <h6>Special Requests:</h6>
                    {% if booking.special_requests %}
                        <p class="text-muted fst-italic">{{ booking.special_requests|linebreaksbr }}</p>
                    {% else %}
                        <p class="text-muted">No special requests were made.</p>
                    {% endif %}
                </div>
            </div>

            {% if booking.payment_transaction %}
            {# Display details if payment transaction exists #}
            <hr class="my-4">
            <div class="row mt-3">
                <div class="col-12">
                    <h5 class="mb-3">Payment Information</h5>
                    <dl class="row">
                        <dt class="col-sm-4">Payment Status:</dt>
                        <dd class="col-sm-8">
                            <span class="badge {% if booking.payment_transaction.payment_status == 'simulated_success' %}bg-success{% else %}bg-danger{% endif %}">
                                {{ booking.payment_transaction.payment_status|underscore_to_space|capfirst }}
                            </span>
                        </dd>

                        <dt class="col-sm-4">Amount Paid:</dt>
                        <dd class="col-sm-8">${{ booking.payment_transaction.amount_paid|floatformat:2 }}</dd>

                        <dt class="col-sm-4">Transaction Date:</dt>
                        <dd class="col-sm-8">{{ booking.payment_transaction.transaction_timestamp|date:"F j, Y, P" }}</dd>

                        {% if booking.payment_transaction.cardholder_name %}
                        <dt class="col-sm-4">Cardholder Name:</dt>
                        <dd class="col-sm-8">{{ booking.payment_transaction.cardholder_name }}</dd>
                        {% endif %}

                        {% if booking.payment_transaction.contact_number_at_payment %}
                        <dt class="col-sm-4">Contact at Payment:</dt>
                        <dd class="col-sm-8">{{ booking.payment_transaction.contact_number_at_payment }}</dd>
                        {% endif %}

                        {% if booking.payment_transaction.card_number_dummy %}
                        <dt class="col-sm-4">Card Used (Dummy):</dt>
                        <dd class="col-sm-8">**** **** **** {{ booking.payment_transaction.card_number_dummy|slice:"-4:" }}</dd>
                        {% endif %}
                    </dl>
                </div>
            </div>

        {% else %} {# No payment transaction exists, check if payment is expected or free #}

            {% if booking.total_price is not None and booking.total_price > 0 and booking.status != 'cancelled' %}
                {# Display payment due message if price > 0 and not cancelled #}
                <hr class="my-4">
                <div class="row mt-3">
                    <div class="col-12">
                        <h5 class="mb-3">Payment Information</h5>
                        <div class="alert alert-warning" role="alert">
                            Payment is due for this booking. Amount: ${{ booking.total_price|floatformat:2 }}.
                            {% if request.user == booking.user and booking.status == 'pending' %} {# Or confirmed but unpaid #}
                                <a href="{% url 'bookings:dummy_payment' booking_id=booking.id %}" class="btn btn-sm btn-success ms-2">Proceed to Payment</a>
                            {% endif %}
                        </div>
                    </div>
                </div>

            {% elif booking.total_price is not None and booking.total_price == 0 and booking.status != 'cancelled' %}
                {# Display free booking message if price is 0 and not cancelled #}
                <hr class="my-4">
                <div class="row mt-3">
                    <div class="col-12">
                        <h5 class="mb-3">Payment Information</h5>
                        <p class="text-muted">This is a free booking, no payment required.</p>
                    </div>
                </div>

            {% endif %} {# End inner if/elif for non-paid bookings #}

        {% endif %} {# End outer if/else #}

            {# --- End Payment Information Section --- #}

        </div>
        <div class="card-footer bg-light text-end">
            {# Cancel Button - only for the user who made the booking and if cancellable #}
            {% if request.user == booking.user %}
                {% if booking.status == 'pending' or booking.status == 'confirmed' %}
                    <form method="post" action="{% url 'bookings:cancel_booking' pk=booking.pk %}" style="display: inline;" onsubmit="return confirm('Are you sure you want to cancel this booking?');">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm btn-outline-danger">Cancel Booking</button>
                    </form>
                {% endif %}
            {% endif %}

            {# Back Button - context-aware #}
            {% if request.user.is_authenticated and request.user.profile.user_type == 'vendor' %}
                <a href="{% url 'listings:vendor_dashboard' %}" class="btn btn-sm btn-secondary ms-2">Back to Dashboard</a>
            {% else %}
                <a href="{% url 'bookings:user_bookings' %}" class="btn btn-sm btn-secondary ms-2">Back to My Bookings</a>
            {% endif %}
        </div>
    </div>

</div> {# End container #}
{% endblock %}
