# users/models.py
from django.db import models
from django.contrib.auth.models import User
from django.db.models.signals import post_save
from django.dispatch import receiver

class Profile(models.Model):
    USER_TYPE_CHOICES = (
        ('tourist', 'Tourist'),
        ('vendor', 'Vendor'),
        ('guide', 'Guide'), # Added based on stakeholder Sam <PERSON>
    )
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)
    user_type = models.CharField(max_length=10, choices=USER_TYPE_CHOICES, default='tourist')
    phone_number = models.CharField(max_length=20, blank=True, null=True) # Ensure this field exists
    bio = models.TextField(blank=True, null=True)
    # Add other profile fields as needed

    def __str__(self):
        return f"{self.user.username}'s Profile ({self.get_user_type_display()})"

# Signal to create or update profile when User is created/saved
@receiver(post_save, sender=User)
def create_or_update_user_profile(sender, instance, created, **kwargs):
    if created:
        Profile.objects.get_or_create(user=instance)
    #instance.profile.save()

# Add this to settings.py if you use this Profile extensively
# AUTH_PROFILE_MODULE = 'users.Profile' # Older way, usually access via user.profile
