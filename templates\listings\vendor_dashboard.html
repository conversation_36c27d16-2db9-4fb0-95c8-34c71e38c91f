{% extends "base.html" %}
{% load static %}

{% block title %}Vendor Dashboard - {{ vendor.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">Vendor Dashboard</h1>

    {# Display messages passed from other views (e.g., listing created successfully) #}
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Vendor Status -->
    <div class="card shadow-sm mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Welcome, {{ vendor.name }}!</h5>
            {# --- MODIFY THIS BUTTON'S URL TAG --- VVVVV #}
            <a href="{% url 'listings:edit_vendor' vendor.pk %}" class="btn btn-sm btn-outline-secondary" {% if not vendor.pk %}style="pointer-events: none; opacity: 0.5;"{% endif %}>
            {# --- END MODIFICATION --- #}
                <i class="fas fa-user-edit me-1"></i> Edit Profile
            </a>
        </div>
        <div class="card-body">
            {% if vendor.is_approved %}
                <div class="alert alert-success mb-0" role="alert">
                    <i class="fas fa-check-circle me-2"></i>Your vendor profile is <strong>approved</strong> and visible to tourists.
                </div>
            {% else %}
                <div class="alert alert-info mb-0" role="alert">
                    <i class="fas fa-info-circle me-2"></i>Your vendor profile is <strong>pending approval</strong> by TasteLocal administrators. Your listings will not be visible until approved.
                </div>
            {% endif %}
            {# Optional: Add link to edit vendor profile details #}
            {# <a href="#" class="btn btn-sm btn-outline-secondary mt-3">Edit Vendor Profile</a> #}
        </div>
    </div>
    <!-- Basic Analytics Dashboard -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-info text-white"> {# Different color for analytics #}
            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Basic Analytics</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                {# Total Listings #}
                <div class="col-md-4 col-sm-6">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <h2 class="card-title display-5 text-primary">{{ analytics_data.total_listings }}</h2>
                            <p class="card-text text-muted"><i class="fas fa-list-alt me-1"></i>Total Listings</p>
                        </div>
                    </div>
                </div>

                {# Total Bookings (All Statuses) #}
                <div class="col-md-4 col-sm-6">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <h2 class="card-title display-5 text-success">{{ analytics_data.total_bookings_all_statuses }}</h2>
                            <p class="card-text text-muted"><i class="fas fa-calendar-check me-1"></i>Total Bookings</p>
                        </div>
                    </div>
                </div>

                {# Average Rating #}
                <div class="col-md-4 col-sm-6">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <h2 class="card-title display-5 text-warning">
                                {% if analytics_data.average_rating > 0 %}
                                    {{ analytics_data.average_rating|floatformat:1 }} <small class="fs-5 text-muted">/ 5</small>
                                {% else %}
                                    N/A
                                {% endif %}
                            </h2>
                            <p class="card-text text-muted"><i class="fas fa-star me-1"></i>Avg. Rating ({{ analytics_data.total_reviews }} reviews)</p>
                        </div>
                    </div>
                </div>

                {# Pending Bookings #}
                <div class="col-md-4 col-sm-6">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <h2 class="card-title display-5 text-secondary">{{ analytics_data.pending_bookings }}</h2>
                            <p class="card-text text-muted"><i class="fas fa-hourglass-half me-1"></i>Pending Bookings</p>
                        </div>
                    </div>
                </div>

                {# Confirmed Bookings #}
                <div class="col-md-4 col-sm-6">
                    <div class="card text-center h-100">
                        <div class="card-body">
                            <h2 class="card-title display-5 text-info">{{ analytics_data.confirmed_bookings }}</h2>
                            <p class="card-text text-muted"><i class="fas fa-calendar-day me-1"></i>Confirmed Bookings</p>
                        </div>
                    </div>
                </div>

                {# Listing Views (Placeholder) #}
                <div class="col-md-4 col-sm-6">
                    <div class="card text-center h-100 bg-light">
                        <div class="card-body">
                            <h2 class="card-title display-5 text-muted">{{ analytics_data.total_listing_views }}</h2>
                            <p class="card-text text-muted"><i class="fas fa-eye me-1"></i>Listing Views</p>
                            <small class="text-muted d-block">(Tracking coming soon)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Basic Analytics Dashboard -->

    <!-- Listings Management -->
    <div class="card shadow-sm mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Your Listings</h5>
            <a href="{% url 'listings:add_listing' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i> Add New Listing
            </a>
        </div>
        <div class="card-body p-0"> {# Remove padding for table flushness #}
            {% if listings %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">Title</th>
                                <th scope="col">Category</th>
                                <th scope="col">Type</th>
                                <th scope="col">Price</th>
                                <th scope="col" class="text-center">Status</th>
                                <th scope="col" class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for listing in listings %}
                            <tr>
                                <td class="align-middle">
                                    <a href="{% url 'listings:listing_detail' pk=listing.pk %}" title="View Listing Page">{{ listing.title }}</a>
                                </td>
                                <td class="align-middle">{{ listing.category.name|default:"-" }}</td>
                                <td class="align-middle">{{ listing.get_listing_type_display }}</td>
                                <td class="align-middle">
                                    {% if listing.price is not None %}
                                        ${{ listing.price|floatformat:2 }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="align-middle text-center">
                                    <span class="badge rounded-pill {% if listing.is_active %}bg-success{% else %}bg-secondary{% endif %} me-1" title="Active Status">
                                        {% if listing.is_active %}Active{% else %}Inactive{% endif %}
                                    </span>
                                    <span class="badge rounded-pill {% if listing.is_bookable %}bg-info text-dark{% else %}bg-light text-dark{% endif %}" title="Bookable Status">
                                        {% if listing.is_bookable %}Bookable{% else %}Not Bookable{% endif %}
                                    </span>
                                </td>
                                <td class="align-middle text-end">
                                    <a href="{% url 'listings:listing_detail' pk=listing.pk %}" class="btn btn-sm btn-outline-secondary" title="View">
                                        <i class="fas fa-eye"></i> <span class="d-none d-md-inline">View</span>
                                    </a>
                                    {# Add Edit link - assumes you create an edit_listing view/URL #}
                                   <a href="{% url 'listings:edit_listing' pk=listing.pk %}" class="btn btn-sm btn-outline-primary ms-1" title="Edit">
                                        <i class="fas fa-edit"></i> <span class="d-none d-md-inline">Edit</span>
                                    </a>
                                    {# Optional: Add Delete/Deactivate button with confirmation #}
                                    {# <button class="btn btn-sm btn-outline-danger ms-1" title="Delete/Deactivate">...</button> #}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="p-4 text-center">
                    <p class="text-muted">You haven't added any listings yet.</p>
                    <a href="{% url 'listings:add_listing' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Add Your First Listing
                    </a>
                    {# Ensure 'listings:add_listing' is correctly defined in your Django project's urls.py #}
                </div>
            {% endif %}
        </div> {# End card-body #}
    </div> {# End Listings Card #}

 <!-- Booking Management -->
 <div class="card shadow-sm mb-4">
    <div class="card-header">
        <h5 class="mb-0">Booking Requests & Management</h5>
    </div>
    <div class="card-body p-0">
        {% if bookings %}
            <div class="table-responsive">
                <table class="table table-striped table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th scope="col">Listing</th>
                            <th scope="col">Customer</th> {# This column will be modified #}
                            <th scope="col">Date</th>
                            <th scope="col" class="text-center">Guests</th>
                            <th scope="col" class="text-center">Status</th>
                            <th scope="col" class="text-center">Payment</th>
                            <th scope="col" class="text-end">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for booking in bookings %}
                        <tr>
                            <td class="align-middle">
                                <a href="{% url 'listings:listing_detail' pk=booking.listing.pk %}" title="View Listing">{{ booking.listing.title }}</a>
                            </td>
                            {# --- MODIFIED CUSTOMER COLUMN --- #}
                            <td class="align-middle">
                                {% if booking.user.profile.avatar %}
                                    <img src="{{ booking.user.profile.avatar.url }}" alt="{{ booking.user.username }}'s avatar" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                {% else %}
                                    <img src="{% static 'images/default_avatar.png' %}" alt="Default avatar" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                {% endif %}
                                {# --- Tooltip for additional info --- #}

                                <span data-bs-toggle="tooltip"
                                      data-bs-placement="top"
                                      data-bs-html="true" {# Allow HTML in tooltip for line breaks #}
                                      title="{% spaceless %}
                                                {% if booking.user.get_full_name or booking.user.profile.phone_number %}
                                                    {% if booking.user.get_full_name %}
                                                        <strong>Name:</strong> {{ booking.user.get_full_name }}
                                                        {% if booking.user.profile.phone_number %}<br>{% endif %} {# Add line break only if phone also exists #}
                                                    {% endif %}
                                                    {% if booking.user.profile.phone_number %}
                                                        <strong>Phone:</strong> {{ booking.user.profile.phone_number }}
                                                    {% endif %}
                                                {% else %}
                                                    No additional contact info.
                                                {% endif %}
                                             {% endspaceless %}">
                                    {{ booking.user.username }}
                                </span>
                            </td>
                            {# --- END MODIFIED CUSTOMER COLUMN --- #}

                            <td class="align-middle">
                                {# --- MAKE DATE LINKABLE --- #}
                                <a href="{% url 'bookings:booking_detail' pk=booking.pk %}" class="text-decoration-none">
                                    {{ booking.booking_date|date:"D, M j, Y P" }}
                                </a>
                                {# --- END LINKABLE DATE --- #}
                            </td>
                            <td class="align-middle text-center">{{ booking.number_of_people }}</td>
                            <td class="align-middle text-center">
                                {# ... (status badge) ... #}
                                <span class="badge rounded-pill
                                    {% if booking.status == 'confirmed' %} bg-success
                                    {% elif booking.status == 'pending' %} bg-warning text-dark
                                    {% elif booking.status == 'cancelled' %} bg-danger
                                    {% elif booking.status == 'completed' %} bg-secondary
                                    {% else %} bg-light text-dark {% endif %}">
                                    {{ booking.get_status_display }}
                                </span>
                            </td>
                            <td class="align-middle text-center">
                                {# ... (payment info) ... #}
                                {% if booking.payment_transaction %}
                                    <span class="badge
                                        {% if booking.payment_transaction.payment_status == 'simulated_success' %}bg-success
                                        {% else %}bg-danger{% endif %}">
                                        Paid: ${{ booking.payment_transaction.amount_paid|floatformat:2 }}
                                    </span>
                                    <small class="d-block text-muted" title="{{ booking.payment_transaction.transaction_timestamp|date:'Y-m-d H:i:s' }}">
                                        {{ booking.payment_transaction.transaction_timestamp|timesince }} ago
                                    </small>
                                {% elif booking.total_price > 0 and booking.status != 'cancelled' %}
                                    <span class="badge bg-warning text-dark">Payment Due</span>
                                {% elif booking.total_price == 0 and booking.status != 'cancelled' %}
                                     <span class="badge bg-info text-dark">Free</span>
                                {% else %}
                                    <span class="badge bg-secondary">N/A</span>
                                {% endif %}
                            </td>
                            <td class="align-middle text-end">
                                {# ... (action buttons) ... #}
                                {% if booking.status == 'pending' %}
                                    <form method="post" action="{% url 'bookings:confirm_booking' pk=booking.pk %}" style="display: inline;" onsubmit="return confirm('Confirm this booking request?');">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm btn-success" title="Confirm">
                                            <i class="fas fa-check"></i> <span class="d-none d-lg-inline">Confirm</span>
                                        </button>
                                    </form>
                                    <form method="post" action="{% url 'bookings:reject_booking' pk=booking.pk %}" style="display: inline;" onsubmit="return confirm('Reject this booking request? This will cancel it.');">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm btn-danger ms-1" title="Reject">
                                            <i class="fas fa-times"></i> <span class="d-none d-lg-inline">Reject</span>
                                        </button>
                                    </form>
                                {% else %}
                                    <a href="{% url 'bookings:booking_detail' pk=booking.pk %}" class="btn btn-sm btn-outline-secondary" title="View Details">
                                        <i class="fas fa-eye"></i> <span class="d-none d-lg-inline">Details</span>
                                    </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="p-4 text-center">
                <p class="text-muted">No booking requests or confirmed bookings for your listings yet.</p>
            </div>
        {% endif %}
    </div> {# End card-body #}
</div> {# End Booking Management Card #}
    <!-- Vendor Queries Management -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0"><i class="fas fa-envelope-open-text me-2"></i>Customer Queries</h5>
        </div>
        <div class="card-body p-0">
            {% if vendor_queries %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th scope="col">From User</th>
                                <th scope="col">Subject</th>
                                <th scope="col">Received</th>
                                <th scope="col" class="text-center">Status</th>
                                <th scope="col" class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for query in vendor_queries %}
                            <tr class="{% if not query.is_read_by_vendor and not query.is_replied %}table-warning fw-bold{% endif %}"> {# Highlight unread/unreplied #}
                                <td class="align-middle">
                                    {% if query.user.profile.avatar %}
                                        <img src="{{ query.user.profile.avatar.url }}" alt="{{ query.user.username }}'s avatar" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                    {% else %}
                                        <img src="{% static 'images/default_avatar.png' %}" alt="Default avatar" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                    {% endif %}
                                    {{ query.user.username }}
                                </td>

                                <td class="align-middle">
                                    <a href="#queryDetailModal{{ query.pk }}" data-bs-toggle="modal" title="View Message">
                                        {{ query.subject|truncatechars:50 }}
                                    </a>
                                </td>
                                <td class="align-middle">{{ query.created_at|timesince }} ago</td>
                                <td class="align-middle text-center">
                                    {% if query.is_replied %}
                                        <span class="badge bg-success">Replied</span>
                                    {% else %}
                                        <span class="badge bg-info text-dark">Pending Reply</span>
                                    {% endif %}
                                </td>
                                <td class="align-middle text-end">
                                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#queryDetailModal{{ query.pk }}">
                                        <i class="fas fa-eye me-1"></i> View & Reply
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {# Modals for Query Details and Reply #}
                {% for query in vendor_queries %}
                <div class="modal fade" id="queryDetailModal{{ query.pk }}" tabindex="-1" aria-labelledby="queryDetailModalLabel{{ query.pk }}" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title d-flex align-items-center" id="queryDetailModalLabel{{ query.pk }}">
                                    {% if query.user.profile.avatar %}
                                        <img src="{{ query.user.profile.avatar.url }}" alt="{{ query.user.username }}'s avatar" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                    {% else %}
                                        <img src="{% static 'images/default_avatar.png' %}" alt="Default avatar" class="rounded-circle me-2" style="width: 30px; height: 30px; object-fit: cover;">
                                    {% endif %}
                                    Query from {{ query.user.username|default:"Unknown User" }}
                                    <span class="fw-normal mx-1">-</span> {{ query.subject|default:"No Subject" }}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p><strong>Received:</strong> {{ query.created_at|date:"F j, Y, P" }}</p>
                                <p><strong>Message:</strong></p>
                                <blockquote class="blockquote bg-light p-3 rounded">
                                    <p class="mb-0">{{ query.message|linebreaksbr|default:"No message provided." }}</p>
                                </blockquote>
                                <hr>
                                {% if query.is_replied %}
                                    <h6 class="text-success">Your Reply ({{ query.replied_at|date:"F j, Y, P" }}):</h6>
                                    <blockquote class="blockquote bg-light p-3 rounded border-start border-success border-3">
                                        <p class="mb-0">{{ query.reply|linebreaksbr|default:"No reply provided." }}</p>
                                    </blockquote>
                                {% else %}
                                    <h6 class="text-primary">Reply to this Query:</h6>
                                    <form method="post" action="{% url 'listings:reply_to_query' query.pk %}">
                                        {% csrf_token %}
                                        <div class="mb-3">
                                            <textarea name="reply_message" class="form-control" rows="4" placeholder="Type your reply here..." required></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-paper-plane me-1"></i> Send Reply
                                        </button>
                                    </form>
                                    <!-- Ensure 'listings:reply_to_query' is defined in urls.py and query.pk is valid -->
                                {% endif %}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div> <!-- End modal-content -->
                    </div> <!-- End modal-dialog -->
                </div> <!-- End modal -->
                {% endfor %}
            {% else %}
                <div class="p-4 text-center">
                    <p class="text-muted">You have no customer queries at the moment.</p>
                </div>
            {% endif %}
        </div> {# End card-body #}
    </div> {# End Vendor Queries Card #}
</div> {# End container #}


{# Font Awesome for Icons (Optional - Add to base.html if used widely) #}
{% block extra_head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
{% endblock %}

{% endblock %}
{% block extra_scripts %}
{{ block.super }} {# If base.html has an extra_scripts block, include its content #}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize Bootstrap Tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                boundary: document.body // Or a more specific container
            });
        });

        // If you have modals that might hide tooltips, you might need to hide them manually
        // For example, when a modal is about to be shown:
        // document.querySelectorAll('.modal').forEach(modal => {
        //     modal.addEventListener('show.bs.modal', function () {
        //         tooltipList.forEach(tooltip => tooltip.hide());
        //     });
        // });
    });
</script>
{% endblock %}