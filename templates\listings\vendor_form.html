{% extends "base.html" %}
{% load static %}

{% block title %}{% if form.instance.pk %}Edit Vendor Profile{% else %}Become a Vendor{% endif %} - TasteLocal{% endblock %}


{% block content %}
<div class="container mt-4 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">

            {# --- Adjust Breadcrumbs --- #}
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                  <li class="breadcrumb-item"><a href="{% url 'listings:home' %}">Home</a></li>
                  {% if form.instance.pk %}
                    <li class="breadcrumb-item"><a href="{% url 'listings:vendor_dashboard' %}">Vendor Dashboard</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit Profile</li>
                  {% else %}
                    <li class="breadcrumb-item"><a href="{% url 'users:profile' %}">My Profile</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Become a Vendor</li>
                  {% endif %}
                </ol>
            </nav>

            <div class="card shadow-lg">
                {# --- Adjust Card Header --- #}
               <div class="card-header {% if form.instance.pk %}bg-primary{% else %}bg-success{% endif %} text-white">
                   <h2 class="card-title mb-0">
                       <i class="fas {% if form.instance.pk %}fa-user-edit{% else %}fa-store{% endif %} me-2"></i>
                       {% if form.instance.pk %}Edit Vendor Profile{% else %}Register Your Business{% endif %}
                   </h2>
               </div>
               <div class="card-body p-4">
                   {% if not form.instance.pk %} {# Only show this message when adding #}
                   <p class="lead text-muted">Fill out the details below to create your vendor profile. Your profile will be reviewed by an administrator before appearing publicly.</p>
                   <hr>
                   {% endif %}

                   <form method="post" enctype="multipart/form-data" novalidate>
                       {% csrf_token %}

                       {# --- Business Details --- #}
                       <h5 class="text-primary mb-3"><i class="fas fa-info-circle me-2"></i>Business Details</h5>
                       {# Name #}
                       <div class="mb-3">
                           {% with field=form.name %}
                               <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                               {{ field.as_widget }} {# <-- USE WIDGET RENDERING #}
                               {% if field.errors %}
                                   <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div> {# Use d-block for safety #}
                               {% endif %}
                           {% endwith %}
                       </div>
                       {# Description #}
                       <div class="mb-3">
                            {% with field=form.description %}
                               <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                               {{ field.as_widget }} {# <-- USE WIDGET RENDERING #}
                               {% if field.errors %}
                                   <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                               {% endif %}
                           {% endwith %}
                       </div>

                       <hr class="my-4">

                       {# --- Contact & Location --- #}
                       <h5 class="text-primary mb-3"><i class="fas fa-map-marker-alt me-2"></i>Contact & Location</h5>
                       {# Address #}
                       <div class="mb-3">
                           {% with field=form.address %}
                               <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                               {{ field.as_widget }} {# <-- USE WIDGET RENDERING #}
                               {% if field.errors %}
                                   <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                               {% endif %}
                           {% endwith %}
                       </div>
                       {# Phone & Website (Side-by-side) #}
                       <div class="row mb-3">
                           <div class="col-md-6">
                               {% with field=form.phone_number %}
                                   <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                   <div class="input-group {% if field.errors %}has-validation{% endif %}">
                                       <span class="input-group-text bg-light"><i class="fas fa-phone text-secondary"></i></span>
                                       {{ field.as_widget }} {# <-- USE WIDGET RENDERING #}
                                       {% if field.errors %}
                                           <div class="invalid-feedback">{{ field.errors|striptags }}</div> {# Input group handles block display #}
                                       {% endif %}
                                   </div>
                               {% endwith %}
                           </div>
                           <div class="col-md-6">
                               {% with field=form.website %}
                                   <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                   <div class="input-group {% if field.errors %}has-validation{% endif %}">
                                       <span class="input-group-text bg-light"><i class="fas fa-globe text-secondary"></i></span>
                                        {{ field.as_widget }} {# <-- USE WIDGET RENDERING #}
                                       {% if field.errors %}
                                           <div class="invalid-feedback">{{ field.errors|striptags }}</div>
                                       {% endif %}
                                   </div>
                                    {% if field.help_text %}
                                       <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                   {% endif %}
                               {% endwith %}
                           </div>
                       </div>
                       {# Opening Hours #}
                       <div class="mb-3">
                            {% with field=form.opening_hours %}
                               <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                               {{ field.as_widget }} {# <-- USE WIDGET RENDERING #}
                               {% if field.errors %}
                                   <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                               {% endif %}
                           {% endwith %}
                       </div>
                       {# Latitude & Longitude (Side-by-side) #}
                        <div class="row mb-3">
                           <div class="col-md-6">
                               {% with field=form.latitude %}
                                   <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                   {{ field.as_widget }} {# <-- USE WIDGET RENDERING #}
                                   {% if field.errors %}
                                       <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                   {% endif %}
                                    {% if field.help_text %}
                                       <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                   {% endif %}
                               {% endwith %}
                           </div>
                           <div class="col-md-6">
                               {% with field=form.longitude %}
                                   <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                   {{ field.as_widget }} {# <-- USE WIDGET RENDERING #}
                                   {% if field.errors %}
                                       <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                   {% endif %}
                                    {% if field.help_text %}
                                       <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                   {% endif %}
                               {% endwith %}
                           </div>
                       </div>

                       {# --- Logo Upload Section --- #}
                       <hr class="my-4">
                       <h5 class="text-info mb-3"><i class="fas fa-image me-2"></i>Business Logo/Image</h5>
                        <div class="mb-3 p-3 border bg-light rounded">
                           {% with field=form.logo %}
                               <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                               {{ field }} {# <-- USE {{ field }} for ClearableFileInput #}
                               {% if field.errors %}
                                   <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                               {% endif %}
                               {% if field.help_text %}
                                   <div class="form-text text-muted mt-1"><small>{{ field.help_text|safe }}</small></div>
                               {% endif %}
                               {# Preview JS can remain the same #}
                               <div id="logoPreviewContainer" class="mt-2"></div>
                           {% endwith %}
                       </div>


                       {# --- Adjust Buttons --- #}
                       <div class="d-grid gap-2 d-md-flex justify-content-md-end border-top pt-4 mt-4">
                           {# Cancel button always goes to dashboard if editing or adding #}
                           <a href="{% url 'listings:vendor_dashboard' %}" class="btn btn-outline-secondary me-md-2">
                               <i class="fas fa-times me-1"></i>Cancel
                           </a>
                           <button type="submit" class="btn {% if form.instance.pk %}btn-primary{% else %}btn-success{% endif %} btn-lg">
                               <i class="fas {% if form.instance.pk %}fa-save{% else %}fa-check{% endif %} me-2"></i>
                               {% if form.instance.pk %}Save Changes{% else %}Submit Vendor Application{% endif %}
                           </button>
                       </div>
                   </form>
               </div> {# End card-body #}
           </div> {# End card #}
       </div> {# End col #}
   </div> {# End row #}
</div> {# End container #}
{% endblock %}
{# Optional: Add JS for image preview if desired #}
{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Find the actual input element generated by ClearableFileInput
    const logoInput = document.querySelector('input[type="file"][name="{{ form.logo.name }}"]');
    const previewContainer = document.getElementById('logoPreviewContainer');

    if (logoInput && previewContainer) {
        logoInput.onchange = evt => {
            // Clear previous preview
            previewContainer.innerHTML = '';

            const [file] = logoInput.files;
            if (file) {
                const preview = document.createElement('img');
                preview.src = URL.createObjectURL(file);
                preview.alt = 'Logo preview';
                preview.className = 'img-thumbnail'; // Add Bootstrap class
                preview.style.maxHeight = '100px';
                preview.style.marginTop = '10px';
                preview.style.display = 'block';
                previewContainer.appendChild(preview);
            }
        }
    }
});
</script>
{% endblock %}
