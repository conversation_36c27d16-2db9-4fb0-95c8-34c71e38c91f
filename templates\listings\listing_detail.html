{% extends "base.html" %}
{% load static %}

{% block title %}{{ listing.title }} - TasteLocal{% endblock %}

{% block content %}
<div class="container mt-4">

    {# Optional Breadcrumbs #}
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="{% url 'listings:home' %}">Home</a></li>
          <li class="breadcrumb-item"><a href="{% url 'listings:listing_list' %}">Explore</a></li>
          {% if listing.category %}
          <li class="breadcrumb-item"><a href="{% url 'listings:listing_list' %}?category={{ listing.category.slug }}">{{ listing.category.name }}</a></li>
          {% endif %}
          <li class="breadcrumb-item active" aria-current="page">{{ listing.title|truncatechars:30 }}</li>
        </ol>
    </nav>

    {# Display messages (e.g., review submitted successfully) #}
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    {# --- ADD THIS CONTAINER FOR AJAX MESSAGES --- #}
    <div id="ajax-messages-container" class="mb-3">
        <!-- AJAX success/error messages will be injected here -->
    </div>
    {# --- END AJAX MESSAGES CONTAINER --- #}

    <div class="row g-4">
        <!-- Listing Details Column -->
        <div class="col-lg-7">
            <div class="card shadow-sm mb-4">
                {% if listing.image %}
                    <img src="{{ listing.image.url }}" class="card-img-top" alt="{{ listing.title }}" style="max-height: 400px; object-fit: cover;">
                {% else %}
                    <img src="https://via.placeholder.com/700x400.png?text={{ listing.title|urlencode }}" class="card-img-top" alt="Placeholder Image">
                {% endif %}
                <div class="card-body">
                    {# --- WRAP TITLE AND FAVORITE BUTTON IN A FLEX CONTAINER --- #}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h1 class="card-title mb-0">{{ listing.title }}</h1> {# Added mb-0 to h1 to prevent extra space #}
                        {# --- FAVORITE BUTTON (MOVED HERE) --- #}
                        <div id="favorite-button-container"> {# Keep the container for JS targeting if needed #}
                            {% if user.is_authenticated %}
                                <form method="post" action="{% url 'listings:toggle_favorite_listing' listing_pk=listing.pk %}" id="favorite-listing-form" class="d-inline">
                                    {% csrf_token %}
                                    {% if is_favorited %}
                                        <button type="submit" class="btn btn-danger btn-sm" title="Remove from Favorites"> {# Made button btn-sm #}
                                            <i class="fas fa-heart-broken me-1"></i> Unfavorite
                                        </button>
                                    {% else %}
                                        <button type="submit" class="btn btn-outline-danger btn-sm" title="Add to Favorites"> {# Made button btn-sm #}
                                            <i class="fas fa-heart me-1"></i> Favorite
                                        </button>
                                    {% endif %}
                                </form>
                            {% else %}
                                <a href="{% url 'login' %}?next={{ request.path }}" class="btn btn-outline-danger btn-sm" title="Log in to Favorite"> {# Made button btn-sm #}
                                    <i class="fas fa-heart me-1"></i> Favorite
                                </a>
                            {% endif %}
                        </div>
                        {# --- END FAVORITE BUTTON --- #}
                    </div>
                    {# --- END FLEX CONTAINER --- #}

                    <p class="card-text text-muted">
                        By <a href="{% url 'listings:vendor_detail' pk=listing.vendor.pk %}" class="text-decoration-none">{{ listing.vendor.name }}</a>
                        {% if listing.category %}
                         in <span class="badge bg-secondary">{{ listing.category.name }}</span>
                        {% endif %}
                    </p>

                    {% if listing.average_rating %}
                        <p class="card-text">
                            <span class="text-warning">
                                {% with rating=listing.average_rating|floatformat:0 %}
                                    {% for i in "x"|rjust:rating %}★{% endfor %}
                                {% endwith %}
                            </span>
                            <span class="text-muted">
                                {% with rating=listing.average_rating|floatformat:0 %}
                                    {% with remaining=rating|add:"-5" %}
                                        {% for i in "x"|rjust:remaining|slice:"1:" %}☆{% endfor %}
                                    {% endwith %}
                                {% endwith %}
                            </span>
                            <span class="ms-1">{{ listing.average_rating|floatformat:1 }} / 5</span>
                            <span class="text-muted ms-2">({{ reviews.count }} review{{ reviews.count|pluralize }})</span>
                        </p>
                    {% else %}
                        <p class="text-muted">No reviews yet.</p>
                    {% endif %}

                    <hr>

                    <p class="card-text fs-5">{{ listing.description|linebreaksbr }}</p>

                    <ul class="list-group list-group-flush mb-3">
                        <li class="list-group-item ps-0"><strong>Type:</strong> {{ listing.get_listing_type_display }}</li>
                        {% if listing.price is not None %}
                        <li class="list-group-item ps-0"><strong>Price:</strong> ${{ listing.price|floatformat:2 }} {% if listing.listing_type == 'tour' or listing.listing_type == 'experience' %}per person{% endif %}</li>
                        {% endif %}
                        {% if listing.duration_minutes %}
                        <li class="list-group-item ps-0"><strong>Duration:</strong> {{ listing.duration_minutes }} minutes</li>
                        {% endif %}
                    </ul>

                    {% if listing.is_bookable %}
                        {% if user.is_authenticated %}
                            <a href="{% url 'bookings:create_booking' listing_id=listing.pk %}" class="btn btn-success btn-lg">
                                <i class="fas fa-calendar-check me-2"></i>Book Now
                            </a>
                        {% else %}
                            <a href="{% url 'login' %}?next={{ request.path }}" class="btn btn-success btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Log in to Book
                            </a>
                        {% endif %}
                    {% else %}
                        <p class="text-info">Booking not available directly through TasteLocal. Please contact the vendor.</p>
                    {% endif %}
               
                </div> {# End card-body #}
            </div> {# End card #}
        </div> {# End Listing Details Column #}

        <!-- Reviews and Add Review Column -->
        <div class="col-lg-5">
            <!-- Reviews Section -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h4 class="mb-0">Reviews ({{ reviews.count }})</h4>
                </div>
                <div class="card-body">
                    {% if reviews %}
                        <ul class="list-unstyled">
                            {% for review in reviews %}
                            <li class="mb-3 pb-3 border-bottom">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <strong class="text-primary">{{ review.user.username }}</strong>
                                    <small class="text-muted">{{ review.created_at|timesince }} ago</small>
                                </div>
                                <p class="mb-1">
                                    <span class="text-warning">{% for i in "x"|rjust:review.rating %}★{% endfor %}</span>
                                    <span class="text-muted">{% with remaining=review.rating|add:"-5" %}{% for i in "x"|rjust:remaining|slice:"1:" %}☆{% endfor %}{% endwith %}</span>
                                </p>
                                <p class="mb-0 fst-italic">"{{ review.comment|linebreaksbr }}"</p>
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-muted">Be the first to review this listing!</p>
                    {% endif %}
                </div>
            </div>

            <!-- Add Review Section -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h4 class="mb-0">Leave a Review</h4>
                </div>
                <div class="card-body">
                    {% if user.is_authenticated %}
                        {% if user_review %}
                            <div class="alert alert-info" role="alert">
                                You have already reviewed this listing.
                                <hr>
                                <p class="mb-1"><strong>Your Rating:</strong>
                                    <span class="text-warning">{% for i in "x"|rjust:user_review.rating %}★{% endfor %}</span>
                                    <span class="text-muted">{% with remaining=user_review.rating|add:"-5" %}{% for i in "x"|rjust:remaining|slice:"1:" %}☆{% endfor %}{% endwith %}</span>
                                </p>
                                <p class="mb-0 fst-italic">"{{ user_review.comment }}"</p>
                                {# Optional: Add link to edit review #}
                            </div>
                        {% else %}
                            <form method="post" action="{% url 'listings:add_review' listing_id=listing.pk %}" novalidate>
                                {% csrf_token %}

                                {# Display Non-Field Errors #}
                                {% if review_form.non_field_errors %}
                                    <div class="alert alert-danger" role="alert">
                                        {% for error in review_form.non_field_errors %}
                                            <p class="mb-0">{{ error }}</p>
                                        {% endfor %}
                                    </div>
                                {% endif %}

                                {# Rating Field #}
                                <div class="mb-3">
                                    <label for="{{ review_form.rating.id_for_label }}" class="form-label">{{ review_form.rating.label }}</label>
                                    {{ review_form.rating.as_widget }} {# Render select widget #}
                                    {% if review_form.rating.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in review_form.rating.errors %} {{ error }} {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                {# Comment Field #}
                                <div class="mb-3">
                                     <label for="{{ review_form.comment.id_for_label }}" class="form-label">{{ review_form.comment.label }}</label>
                                     <textarea name="{{ review_form.comment.name }}"
                                              id="{{ review_form.comment.id_for_label }}"
                                              class="form-control {% if review_form.comment.errors %}is-invalid{% endif %}"
                                              rows="4" required>{{ review_form.comment.value|default:'' }}</textarea>
                                    {% if review_form.comment.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in review_form.comment.errors %} {{ error }} {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <button type="submit" class="btn btn-primary">Submit Review</button>
                            </form>
                        {% endif %}
                    {% else %}
                        <p class="text-muted">Please <a href="{% url 'login' %}?next={{ request.path }}">log in</a> to leave a review.</p>
                    {% endif %}
                </div> {# End card-body #}
            </div> {# End Add Review Card #}

        </div> {# End Reviews Column #}
    </div> {# End row #}
</div> {# End container #}

{# Font Awesome for Icons (Optional - Add to base.html if used widely) #}
{% block extra_head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
{% endblock %}

{% endblock %}

{% block extra_scripts %}
{{ block.super }} {# Include content from base.html's extra_scripts if any #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const favoriteForm = document.getElementById('favorite-listing-form');
    const ajaxMessagesContainer = document.getElementById('ajax-messages-container'); // Get the new container

    if (favoriteForm) {
        favoriteForm.addEventListener('submit', function(event) {
            event.preventDefault(); // Prevent traditional form submission

            const formData = new FormData(favoriteForm);
            const actionUrl = favoriteForm.getAttribute('action');
            const csrfToken = favoriteForm.querySelector('input[name="csrfmiddlewaretoken"]').value;

            // Clear previous AJAX messages
            if(ajaxMessagesContainer) {
                ajaxMessagesContainer.innerHTML = '';
            }

            fetch(actionUrl, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(errData => {
                        throw new Error(errData.message || 'Network response was not ok: ' + response.statusText);
                    }).catch(() => {
                        throw new Error('Network response was not ok: ' + response.statusText);
                    });
                }
                return response.json();
            })
            .then(data => {
                // Update the button
                const button = favoriteForm.querySelector('button[type="submit"]');
                if (data.is_favorited) {
                    button.classList.remove('btn-outline-danger');
                    button.classList.add('btn-danger');
                    button.innerHTML = '<i class="fas fa-heart-broken me-1"></i> Unfavorite';
                    button.title = 'Remove from Favorites';
                } else {
                    button.classList.remove('btn-danger');
                    button.classList.add('btn-outline-danger');
                    button.innerHTML = '<i class="fas fa-heart me-1"></i> Favorite';
                    button.title = 'Add to Favorites';
                }

                // --- DISPLAY SUCCESS MESSAGE ---
                if (data.message && ajaxMessagesContainer) {
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show'; // Use success class
                    alertDiv.setAttribute('role', 'alert');
                    alertDiv.innerHTML = `
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    ajaxMessagesContainer.appendChild(alertDiv);

                    // Optional: Auto-dismiss the alert after a few seconds
                    setTimeout(() => {
                        // Check if the alert still exists before trying to close
                        if (alertDiv.parentElement) {
                             const bsAlert = bootstrap.Alert.getInstance(alertDiv);
                             if (bsAlert) {
                                 bsAlert.close();
                             } else {
                                // Fallback if getInstance is null (e.g., element removed by other means)
                                // or if Bootstrap's Alert component wasn't fully initialized on it.
                                // This can happen if the element is rapidly created and removed.
                                // A simple removal is safer in such edge cases.
                                 alertDiv.remove();
                             }
                        }
                    }, 5000); // Dismiss after 5 seconds
                }
                // --- END DISPLAY SUCCESS MESSAGE ---

            })
            .catch(error => {
                console.error('Error toggling favorite:', error);
                // Display error message in the AJAX container
                if (ajaxMessagesContainer) {
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                    alertDiv.setAttribute('role', 'alert');
                    alertDiv.innerHTML = `
                        An error occurred: ${error.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    ajaxMessagesContainer.appendChild(alertDiv);
                } else {
                    alert('An error occurred while updating your favorite: ' + error.message); // Fallback
                }
            });
        });
    }
});
</script>
{% endblock %}
