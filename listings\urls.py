# listings/urls.py
from django.urls import path
from . import views

app_name = 'listings' # Namespace for URLs

urlpatterns = [
    path('', views.home, name='home'), # Homepage
    path('listings/', views.ListingListView.as_view(), name='listing_list'),
    path('listing/<int:pk>/', views.ListingDetailView.as_view(), name='listing_detail'),
    path('listing/<int:listing_id>/review/', views.add_review, name='add_review'),
    path('vendor/<int:pk>/', views.VendorDetailView.as_view(), name='vendor_detail'),
    # Add URLs for vendor dashboard, adding/editing listings later
    path('vendor/dashboard/', views.vendor_dashboard, name='vendor_dashboard'),
    path('vendor/add/', views.add_vendor, name='add_vendor'), 
    path('vendor/edit/<int:pk>', views.edit_vendor, name='edit_vendor'),
    path('vendor/add_listing/', views.add_listing, name='add_listing'),
    path('listing/<int:pk>/edit/', views.edit_listing, name='edit_listing'),
        # --- ADD STAFF VENDOR MANAGEMENT URLS ---
    path('vendor/<int:vendor_pk>/staff_approve/', views.staff_approve_vendor, name='staff_approve_vendor'),
    path('vendor/<int:vendor_pk>/staff_disapprove/', views.staff_disapprove_vendor, name='staff_disapprove_vendor'),
    # --- END OF STAFF URLS ---
    path('vendor/<int:vendor_pk>/query/', views.submit_vendor_query, name='submit_vendor_query'),
    path('query/<int:query_pk>/reply/', views.reply_to_query, name='reply_to_query'), # <-- ADD THIS
    path('listing/<int:listing_pk>/toggle_favorite/', views.toggle_favorite_listing, name='toggle_favorite_listing'),
    path('vendor/<int:vendor_pk>/toggle_favorite/', views.toggle_favorite_vendor, name='toggle_favorite_vendor'),

]
