from django import template
from django.utils.safestring import mark_safe

register = template.Library()

@register.filter(name='replace_char')
def replace_char(value, arg):
    """
    Replaces all occurrences of arg (arg.split(':')[0])
    with arg.split(':')[1] in the given string.
    Example usage: {{ my_string|replace_char:"_:" }}  (replaces underscore with space)
    Example usage: {{ my_string|replace_char:"old:new" }}
    """
    if not isinstance(value, str):
        return value # Or handle as an error, or convert to string

    if ':' not in arg:
        # Default to replacing with space if only one char is given for 'old'
        old_char = arg
        new_char = ' '
    else:
        parts = arg.split(':', 1)
        if len(parts) == 2:
            old_char, new_char = parts
        else: # Should not happen if format is "old:new"
            return value

    return value.replace(old_char, new_char)

# You can also create a simpler one if you only ever replace underscores with spaces
@register.filter(name='underscore_to_space')
def underscore_to_space(value):
    if not isinstance(value, str):
        return value
    return value.replace('_', ' ')

