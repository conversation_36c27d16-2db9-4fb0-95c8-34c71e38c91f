# bookings/models.py
from django.db import models
from django.contrib.auth.models import User
from listings.models import Listing # Import Listing model

class Booking(models.Model):    
    STATUS_CHOICES = (
        ('pending', 'Pending Confirmation'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
    )
    listing = models.ForeignKey(Listing, on_delete=models.CASCADE, related_name='bookings', limit_choices_to={'is_bookable': True})
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bookings') # User making the booking
    booking_date = models.DateTimeField(help_text="Requested date and time for the booking")
    number_of_people = models.PositiveSmallIntegerField(default=1)
    special_requests = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='pending')
    total_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, help_text="Calculated price at time of booking")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    def __str__(self):
        return f"Booking for {self.listing.title} by {self.user.username} on {self.booking_date.strftime('%Y-%m-%d %H:%M')}"

    def save(self, *args, **kwargs):
        # Optional: Calculate total price if listing has a price
        if self.listing.price and not self.total_price:
             self.total_price = self.listing.price * self.number_of_people
        super().save(*args, **kwargs) # Call the "real" save() method.


class PaymentTransaction(models.Model):
    booking = models.OneToOneField(Booking, on_delete=models.CASCADE, related_name='payment_transaction')
    cardholder_name = models.CharField(
        max_length=255,
        help_text="Name on the card",
        blank=True, # Allow blank for existing records
        null=True   # Allow null for existing records
    )
    contact_number_at_payment = models.CharField(
        max_length=50,
        help_text="Contact number provided at payment",
        blank=True, # Allow blank for existing records
        null=True   # Allow null for existing records
    )
    # For dummy purposes, we'll store these as CharFields.
    # In a REAL system, you would NEVER store full card numbers.
    # You'd store a transaction ID from a payment gateway, last 4 digits, card type, etc.
    card_number_dummy = models.CharField(max_length=20, help_text="Dummy card number")
    expiry_date_dummy = models.CharField(max_length=7, help_text="Dummy expiry MM/YY")
    cvc_dummy = models.CharField(max_length=4, help_text="Dummy CVC")
    amount_paid = models.DecimalField(max_digits=10, decimal_places=2)
    transaction_timestamp = models.DateTimeField(auto_now_add=True)
    payment_status = models.CharField(max_length=20, default="simulated_success") # e.g., success, failed


    def __str__(self):
        return f"Payment for Booking #{self.booking.id} - Amount: ${self.amount_paid}"

    class Meta:
        ordering = ['-transaction_timestamp']
# --- END OF ADDED MODEL ---