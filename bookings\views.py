# bookings/views.py
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
# Add this line VVVVV
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views import generic
from django.urls import reverse_lazy
from django.utils import timezone
from listings.models import Listing, Vendor # <-- Import Vendor here
from .models import Booking
from .forms import BookingForm, DummyPaymentForm # <-- Import DummyPaymentForm
from .models import Booking, PaymentTransaction # <-- Import PaymentTransaction

from django.views.decorators.http import require_POST # <-- Add this import
from django.db.models import Q # Import Q objects for complex lookups
@login_required
def create_booking(request, listing_id):
    listing = get_object_or_404(Listing, pk=listing_id, is_active=True, is_bookable=True, vendor__is_approved=True)

    if request.method == 'POST':
        form = BookingForm(request.POST)
        if form.is_valid():
            booking = form.save(commit=False)
            booking.listing = listing
            booking.user = request.user
            # Basic validation: Ensure booking date is in the future
            if booking.booking_date <= timezone.now():
                messages.error(request, "Booking date must be in the future.")
                # Re-render form with error (better UX than just redirecting)
                return render(request, 'bookings/booking_form.html', {'form': form, 'listing': listing})

            # Calculate price if applicable (can also be done in model's save method)
            if listing.price:
                booking.total_price = listing.price * booking.number_of_people
            else:
                booking.total_price = 0 # Or None

            booking.save()
            messages.success(request, f"Booking request for '{listing.title}' submitted! You will be notified upon confirmation.")
            # Redirect to user's bookings page or booking detail
            return redirect('bookings:dummy_payment', booking_id=booking.id)

        else:
             messages.error(request, "Please correct the errors in the booking form.")
    else:
        # Pre-populate form if needed, e.g., with default number_of_people=1
        form = BookingForm(initial={'number_of_people': 1})

    context = {
        'form': form,
        'listing': listing,
    }
    return render(request, 'bookings/booking_form.html', context)

# --- ADD DUMMY PAYMENT VIEW ---
@login_required
def dummy_payment_view(request, booking_id):
    booking = get_object_or_404(Booking, pk=booking_id, user=request.user)

    if booking.status == 'completed' or hasattr(booking, 'payment_transaction'):
        messages.info(request, f"Booking #{booking.id} for '{booking.listing.title}' is already processed or paid.")
        return redirect('bookings:booking_detail', pk=booking.id)

    amount_to_pay = booking.total_price

    if request.method == 'POST':
        payment_form = DummyPaymentForm(request.POST) # Use the new form
        if payment_form.is_valid():
            # Simulate payment processing
            # Create and save the PaymentTransaction record
            PaymentTransaction.objects.create(
                booking=booking,
                # --- ADD THESE FIELDS ---
                cardholder_name=payment_form.cleaned_data['cardholder_name'],
                contact_number_at_payment=payment_form.cleaned_data['contact_number'],
                # --- END ADDED FIELDS ---
                card_number_dummy=payment_form.cleaned_data['card_number'],
                expiry_date_dummy=payment_form.cleaned_data['expiry_date'],
                cvc_dummy=payment_form.cleaned_data['cvc'],
                amount_paid=amount_to_pay,
                payment_status="simulated_success" # Or get from a real gateway
            )

            # Optionally, update booking status if payment implies confirmation or completion
            # if booking.status == 'pending':
            #     booking.status = 'confirmed'
            #     booking.save()

            messages.success(request, f"Dummy payment of ${amount_to_pay:.2f} for booking #{booking.id} ('{booking.listing.title}') was successful and recorded!")
            return redirect('bookings:booking_detail', pk=booking.id)
        else:
            # If payment_form is invalid, messages are added by the form's error handling
            # and the form is re-rendered below with errors.
            messages.error(request, "Please correct the payment details below.")
    else:
        payment_form = DummyPaymentForm() # Create an empty form for GET request

    context = {
        'booking': booking,
        'amount_to_pay': amount_to_pay,
        'payment_form': payment_form, # Pass the payment form to the template
    }
    return render(request, 'bookings/dummy_payment_page.html', context)

class BookingDetailView(LoginRequiredMixin, generic.DetailView):
    model = Booking
    template_name = 'bookings/booking_detail.html'
    context_object_name = 'booking'

    def get_queryset(self):
        user = self.request.user
        # Base query: user's own bookings OR bookings for listings owned by the user if they are a vendor
        
        user_bookings_q = Q(user=user)
        vendor_listings_bookings_q = Q() # Empty Q object

        if hasattr(user, 'profile') and user.profile.user_type == 'vendor':
            try:
                # Get vendor instance associated with the current user
                vendor_instance = Vendor.objects.get(owner=user)
                vendor_listings_bookings_q = Q(listing__vendor=vendor_instance)
            except Vendor.DoesNotExist:
                pass # User has vendor profile type but no Vendor object yet

        # Combine queries: user can see their own bookings OR bookings for their listings
        combined_q = user_bookings_q | vendor_listings_bookings_q
        
        return Booking.objects.filter(combined_q).select_related(
            'listing', 
            'listing__vendor', 
            'user', # For "Booked By"
            'user__profile', # If you need user's profile details
            'payment_transaction' # Eager load payment transaction
        ).distinct() # Use distinct if Q objects might cause duplicates (though unlikely here with PK lookup)

    #Optional: You can add more context if needed
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add any additional context variables here
        return context
@login_required
def user_bookings(request):
    # Get bookings made by the current user
    bookings = Booking.objects.filter(user=request.user).select_related('listing', 'listing__vendor').order_by('-booking_date')
    context = {
        'bookings': bookings,
        'page_title': "My Bookings"
    }
    return render(request, 'bookings/booking_list.html', context)
# --- ADD THIS VIEW ---
@login_required
@require_POST # Ensures this view can only be accessed via POST request
def cancel_booking(request, pk):
    """ Allows a logged-in user to cancel their own booking if it's cancellable. """
    # Get the booking, ensuring it belongs to the current user
    booking = get_object_or_404(Booking, pk=pk, user=request.user)

    # Check if the booking is in a state that allows cancellation
    if booking.status in ['pending', 'confirmed']:
        # Update the status to 'cancelled'
        booking.status = 'cancelled'
        booking.save()
        messages.success(request, f"Booking #{booking.id} for '{booking.listing.title}' has been successfully cancelled.")
        # Optional: Add notification logic here (e.g., email vendor)
    else:
        # If the booking is already cancelled, completed, etc.
        messages.warning(request, f"Booking #{booking.id} could not be cancelled because its status is '{booking.get_status_display()}'.")

    # Redirect back to the user's list of bookings
    return redirect('bookings:user_bookings')
# --- END OF ADDED VIEW ---

# Add view for vendors to see bookings for their listings
# @login_required
# def vendor_bookings(request): ...
# --- ADD VENDOR BOOKING MANAGEMENT VIEWS ---

@login_required
@require_POST
def confirm_booking(request, pk):
    """ Allows a vendor to confirm a pending booking for their listing. """
    # 1. Verify user is a vendor
    if not hasattr(request.user, 'profile') or request.user.profile.user_type != 'vendor':
        messages.error(request, "You do not have permission to manage bookings.")
        return redirect('listings:home')

    # 2. Get the Vendor associated with the logged-in user
    try:
        # Now 'Vendor' is defined because of the import
        vendor = Vendor.objects.get(owner=request.user)
    except Vendor.DoesNotExist:
        messages.error(request, "Vendor profile not found for your account.")
        return redirect('listings:vendor_dashboard')

    # 3. Get the booking, ensuring it's for this vendor's listing AND is pending
    booking = get_object_or_404(Booking, pk=pk, listing__vendor=vendor, status='pending')

    # 4. Update status
    booking.status = 'confirmed'
    booking.save()
    messages.success(request, f"Booking #{booking.id} for '{booking.listing.title}' confirmed successfully.")
    # Optional: Add notification logic here (e.g., email customer)

    # 5. Redirect back to vendor dashboard
    return redirect('listings:vendor_dashboard')


@login_required
@require_POST
def reject_booking(request, pk):
    """ Allows a vendor to reject (cancel) a pending booking for their listing. """
    # 1. Verify user is a vendor
    if not hasattr(request.user, 'profile') or request.user.profile.user_type != 'vendor':
        messages.error(request, "You do not have permission to manage bookings.")
        return redirect('listings:home')

    # 2. Get the Vendor associated with the logged-in user
    try:
        # Now 'Vendor' is defined because of the import
        vendor = Vendor.objects.get(owner=request.user)
    except Vendor.DoesNotExist:
        messages.error(request, "Vendor profile not found for your account.")
        return redirect('listings:vendor_dashboard')

    # 3. Get the booking, ensuring it's for this vendor's listing AND is pending
    booking = get_object_or_404(Booking, pk=pk, listing__vendor=vendor, status='pending')

    # 4. Update status to 'cancelled' (rejecting means cancelling)
    booking.status = 'cancelled'
    # Optional: You could add a reason field to the Booking model later
    booking.save()
    messages.info(request, f"Booking #{booking.id} for '{booking.listing.title}' has been rejected (cancelled).")
    # Optional: Add notification logic here (e.g., email customer)

    # 5. Redirect back to vendor dashboard
    return redirect('listings:vendor_dashboard')

# --- END OF VENDOR BOOKING MANAGEMENT VIEWS ---