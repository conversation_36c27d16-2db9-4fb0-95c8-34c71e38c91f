{% extends "base.html" %}
{% load static %}

{% block title %}My Profile - {{ user.username }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">My Profile</h1>

    {# Display messages passed from the view (e.g., profile updated successfully) #}
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row g-4">
        <!-- Profile Information and Edit Form -->
        <div class="col-lg-6">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Account Details</h5>
                </div>
                <div class="card-body">
                    {# Display Profile Avatar #}
                    {% if user.profile.avatar %}
                        <div class="text-center mb-3">
                            <img src="{{ user.profile.avatar.url }}" alt="{{ user.username }}'s Avatar" class="img-fluid rounded-circle" style="width: 120px; height: 120px; object-fit: cover; border: 3px solid #eee;">
                        </div>
                    {% else %}
                        <div class="text-center mb-3">
                            <img src="{% static 'images/default_avatar.png' %}" alt="Default Avatar" class="img-fluid rounded-circle" style="width: 120px; height: 120px; object-fit: cover; border: 3px solid #eee;">
                        </div>
                    {% endif %}
                    <dl class="row mb-0">
                        <dt class="col-sm-4">Username:</dt>
                        <dd class="col-sm-8">{{ user.username }}</dd>

                        <dt class="col-sm-4">Email:</dt>
                        <dd class="col-sm-8">{{ user.email }}</dd>

                        <dt class="col-sm-4">First Name:</dt>
                        <dd class="col-sm-8">{{ user.first_name|default:"-" }}</dd>

                        <dt class="col-sm-4">Last Name:</dt>
                        <dd class="col-sm-8">{{ user.last_name|default:"-" }}</dd>

                        <dt class="col-sm-4">Account Type:</dt>
                        <dd class="col-sm-8">{{ user.profile.get_user_type_display }}</dd>

                        <dt class="col-sm-4">Joined:</dt>
                        <dd class="col-sm-8">{{ user.date_joined|date:"F j, Y" }}</dd>
                    </dl>

                    <hr>
                    <h6 class="card-subtitle mb-3 text-muted">Edit Profile Details</h6>
                    <form method="post" novalidate enctype="multipart/form-data">
                        {% csrf_token %}

                        {# Display Non-Field Errors #}
                        {% if profile_form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in profile_form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        {# Render ProfileForm fields #}
                        {% for field in profile_form %}
                            <div class="mb-3">
                                <label for="{{ field.id_for_label }}" class="form-label">
                                    {{ field.label }}
                                    {% if field.field.required %}
                                        <span class="text-danger">*</span> {# Visual indicator for required field #}
                                    {% endif %}
                                </label>

                                {# Use Django's widget rendering for consistency and to pick up 'required' attribute #}
                                {{ field.as_widget }}

                                {% if field.errors %}
                                    <div class="invalid-feedback d-block"> {# Use d-block for proper display #}
                                        {% for error in field.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if field.help_text %}
                                    <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                {% endif %}
                            </div>
                        {% endfor %}


                        <button type="submit" class="btn btn-success">Save Profile Changes</button>
                        <a href="{% url 'password_change' %}" class="btn btn-outline-secondary ms-2">Change Password</a>
                    </form>
                </div>
            </div>
            {% if user.is_staff and all_vendors_for_staff %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-warning text-dark"> {# Distinct color for staff section #}
                    <h5 class="mb-0"><i class="fas fa-users-cog me-2"></i>Vendor Administration</h5>
                </div>
                <div class="card-body p-0">
                    {% if all_vendors_for_staff %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Vendor Name</th>
                                        <th>Owner</th>
                                        <th class="text-center">Status</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for v_admin in all_vendors_for_staff %}
                                    <tr>
                                        <td class="align-middle">
                                            <a href="{% url 'listings:vendor_detail' pk=v_admin.pk %}" title="View Vendor Page">{{ v_admin.name }}</a>
                                        </td>
                                        <td class="align-middle">{{ v_admin.owner.username }}</td>
                                        <td class="align-middle text-center">
                                            {% if v_admin.is_approved %}
                                                <span class="badge bg-success">Approved</span>
                                            {% else %}
                                                <span class="badge bg-secondary">Pending</span>
                                            {% endif %}
                                        </td>
                                        <td class="align-middle text-end">
                                            {% if v_admin and not v_admin.is_approved %}
                                                <form method="post" action="{% url 'listings:staff_approve_vendor' vendor_pk=v_admin.pk %}" style="display: inline;" onsubmit="return confirm('Approve {{ v_admin.name }}?');">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-sm btn-success" title="Approve">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                            {% if v_admin.is_approved %}
                                                <form method="post" action="{% url 'listings:staff_disapprove_vendor' vendor_pk=v_admin.pk %}" style="display: inline;" onsubmit="return confirm('Disapprove {{ v_admin.name }}? This will make them pending again.');">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-sm btn-warning text-dark" title="Disapprove">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </form>
                                            {% endif %}
                                            {# Optional: Add a delete button if needed, with extreme caution #}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-center text-muted p-3">No vendors found in the system.</p>
                    {% endif %}
                </div>
            </div>
            {% endif %}

        </div>

        <!-- Bookings and Reviews -->
        <div class="col-lg-6">
            {# --- Favorite Listings Card --- #}
            {% if user_favorite_listings %}
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-heart text-danger me-2"></i>My Favorite Listings</h5>
                </div>
                <ul class="list-group list-group-flush">
                    {% for fav_item in user_favorite_listings %}
                    <li class="list-group-item d-flex justify-content-between align-items-center flex-wrap">
                        <div>
                            <a href="{% url 'listings:listing_detail' pk=fav_item.listing.pk %}" class="fw-bold text-decoration-none">{{ fav_item.listing.title }}</a>
                            <small class="d-block text-muted">By: <a href="{% url 'listings:vendor_detail' pk=fav_item.listing.vendor.pk %}" class="text-muted text-decoration-none">{{ fav_item.listing.vendor.name }}</a></small>
                        </div>
                        <form method="post" action="{% url 'listings:toggle_favorite_listing' listing_pk=fav_item.listing.pk %}" class="d-inline mt-2 mt-md-0">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Remove from Favorites">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </form>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% elif user.is_authenticated %}
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center text-muted">
                    You haven't favorited any listings yet. <a href="{% url 'listings:listing_list' %}">Explore now!</a>
                </div>
            </div>
            {% endif %}
            
            {# --- Favorite Vendors Card --- #}
            {% if user_favorite_vendors %}
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-store text-danger me-2"></i>My Favorite Vendors</h5>
                </div>
                <ul class="list-group list-group-flush">
                    {% for fav_item in user_favorite_vendors %}
                    <li class="list-group-item d-flex justify-content-between align-items-center flex-wrap">
                        <a href="{% url 'listings:vendor_detail' pk=fav_item.vendor.pk %}" class="fw-bold text-decoration-none">{{ fav_item.vendor.name }}</a>
                        <form method="post" action="{% url 'listings:toggle_favorite_vendor' vendor_pk=fav_item.vendor.pk %}" class="d-inline mt-2 mt-md-0">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-sm btn-outline-danger" title="Remove from Favorite Vendors">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </form>
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% elif user.is_authenticated %}
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center text-muted">
                    You haven't favorited any vendors yet.
                </div>
            </div>
            {% endif %}            
            <!-- My Bookings -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">My Bookings</h5>
                </div>
                <div class="card-body p-0"> {# Remove padding to allow list group flush #}
                    {% if user_bookings %}
                        <ul class="list-group list-group-flush">
                            {% for booking in user_bookings %}
                            <li class="list-group-item d-flex justify-content-between align-items-center flex-wrap">
                                <div>
                                    <a href="{% url 'listings:listing_detail' pk=booking.listing.pk %}" class="fw-bold text-decoration-none">{{ booking.listing.title }}</a>
                                    <small class="d-block text-muted">{{ booking.booking_date|date:"D, M j, Y, P" }} ({{ booking.number_of_people }} person{{ booking.number_of_people|pluralize }})</small>
                                </div>
                                <div>
                                    <span class="badge rounded-pill
                                        {% if booking.status == 'confirmed' %} bg-success
                                        {% elif booking.status == 'pending' %} bg-warning text-dark
                                        {% elif booking.status == 'cancelled' %} bg-danger
                                        {% elif booking.status == 'completed' %} bg-secondary
                                        {% else %} bg-light text-dark {% endif %} me-2">
                                        {{ booking.get_status_display }}
                                    </span>
                                    <a href="{% url 'bookings:booking_detail' pk=booking.pk %}" class="btn btn-sm btn-outline-secondary">Details</a>
                                </div>
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-center text-muted p-3">You haven't made any bookings yet.</p>
                        <div class="text-center pb-3">
                             <a href="{% url 'listings:listing_list' %}" class="btn btn-sm btn-primary">Explore Experiences</a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- My Reviews -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="mb-0">My Reviews</h5>
                </div>
                 <div class="card-body p-0">
                    {% if user_reviews %}
                        <ul class="list-group list-group-flush">
                            {% for review in user_reviews %}
                            <li class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        <a href="{% url 'listings:listing_detail' pk=review.listing.pk %}" class="text-decoration-none">{{ review.listing.title }}</a>
                                    </h6>
                                    <small class="text-muted">{{ review.created_at|timesince }} ago</small>
                                </div>
                                <p class="mb-1">
                                    <span class="text-warning">{% for i in "x"|rjust:review.rating %}★{% endfor %}</span> {# Simple star rating #}
                                    <span class="text-muted">{% for i in "x"|rjust:review.rating|add:"-5"|slice:"1:" %}☆{% endfor %}</span>
                                    ({{ review.rating }}/5)
                                </p>
                                <p class="mb-1 fst-italic">"{{ review.comment|truncatewords:30 }}"</p>
                                {# Optional: Add link to edit review if implemented #}
                            </li>
                            {% endfor %}
                        </ul>
                    {% else %}
                        <p class="text-center text-muted p-3">You haven't written any reviews yet.</p>
                    {% endif %}
                </div>
            </div>
            {# --- STAFF USER TYPE MANAGEMENT CARD --- #}
            {% if user.is_staff and all_user_profiles_for_staff %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-dark"> {# Distinct color for staff section #}
                    <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>User Type Management</h5>
                </div>
                <div class="card-body p-0">
                    {% if all_user_profiles_for_staff %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Username</th>
                                        <th class="text-center">Current Type</th>
                                        <th class="text-center">Set Type To</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for p_admin in all_user_profiles_for_staff %}
                                    <tr>
                                        <td class="align-middle">{{ p_admin.user.username }}</td>
                                        <td class="align-middle text-center">
                                            <span class="badge
                                                {% if p_admin.user_type == 'vendor' %}bg-success
                                                {% elif p_admin.user_type == 'guide' %}bg-primary
                                                {% else %}bg-secondary{% endif %}">
                                                {{ p_admin.get_user_type_display }}
                                            </span>
                                        </td>
                                        <td class="align-middle text-center">
                                            <form method="post" action="{% url 'users:staff_set_user_type' profile_pk=p_admin.pk %}" class="d-inline-flex align-items-center">
                                                {% csrf_token %}
                                                <select name="user_type" class="form-select form-select-sm me-2" style="width: auto;">
                                                    {% for type_val, type_display in p_admin.USER_TYPE_CHOICES %}
                                                        <option value="{{ type_val }}" {% if p_admin.user_type == type_val %}selected disabled{% endif %}>
                                                            {{ type_display }}
                                                        </option>
                                                    {% endfor %}
                                                </select>
                                                <button type="submit" class="btn btn-sm btn-outline-primary" title="Set Type">
                                                    <i class="fas fa-save"></i>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-center text-muted p-3">No other user profiles found.</p>
                    {% endif %}
                </div>
            </div>
            {% endif %}
            {# --- END OF STAFF USER TYPE MANAGEMENT CARD --- #}
            {# --- Vendor Application Link/Status (for non-staff or staff who are also vendors) --- #}
            <div class="card shadow-sm mb-4">
                 <div class="card-body text-center">
                    {% if is_vendor_owner %}
                        <h5 class="card-title">Vendor Access</h5>
                        <p class="card-text">Manage your listings, view bookings, and update your vendor details.</p>
                        <a href="{% url 'listings:vendor_dashboard' %}" class="btn btn-info">
                            <i class="fas fa-tachometer-alt me-1"></i>Go to Vendor Dashboard
                        </a>
                    {% elif not user.is_staff %} {# Only show apply if not staff and not already a vendor #}
                        <h5 class="card-title">Become a Vendor Partner</h5>
                        <p class="card-text">Register your local food business or experience on TasteLocal to reach more customers.</p>
                        <a href="{% url 'listings:add_vendor' %}" class="btn btn-success">
                            <i class="fas fa-store me-1"></i>Apply to Become a Vendor
                        </a>
                    {% elif user.is_staff and not is_vendor_owner %}
                         <p class="text-muted small">As staff, you can manage vendor approvals above. If you also wish to register as a vendor, you can do so here.</p>
                         <a href="{% url 'listings:add_vendor' %}" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-store me-1"></i>Apply to Become a Vendor (Staff)
                        </a>
                    {% endif %}
                 </div>
            </div>
            {# --- End Vendor Application Link/Status --- #}


        </div>


    </div> {# End row #}
</div> {# End container #}
{% endblock %}
