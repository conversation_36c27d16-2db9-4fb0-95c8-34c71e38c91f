{% extends "base.html" %}
{% load static %}

{% block title %}Register - TasteLocal{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title mb-0 text-center">Create Your TasteLocal Account</h2>
                </div>
                <div class="card-body p-4">
                    <p class="text-muted text-center mb-4">Join our community to discover and share amazing local food experiences!</p>

                    <form method="post" enctype="multipart/form-data" novalidate> {# novalidate prevents browser validation, relying on Django's #}
                        {% csrf_token %}

                        {# Display Non-Field Errors (e.g., password mismatch if not handled by field errors) #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        {# Loop through form fields and render them with Bootstrap styling #}
                        {% for field in form %}
                            <div class="mb-3">
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                {# Render the field widget itself, adding Bootstrap class and handling errors #}
                                <input type="{{ field.field.widget.input_type }}"
                                    name="{{ field.name }}"
                                    id="{{ field.id_for_label }}"
                                    class="form-control {% if field.errors %}is-invalid{% endif %}"
                                    {% if field.field.required %}required{% endif %}
                                    value="{{ field.value|default:'' }}">

                                {# Display Field-Specific Errors #}
                                {% if field.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in field.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}

                                {# Display Help Text #}
                                {% if field.help_text %}
                                    <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                {% endif %}
                            </div>
                        {% endfor %}

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">Register</button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-0">Already have an account? <a href="{% url 'login' %}">Log In</a></p>
                    </div>
                </div> {# End card-body #}
            </div> {# End card #}
        </div> {# End col #}
    </div> {# End row #}
</div> {# End container #}
{% endblock %}
