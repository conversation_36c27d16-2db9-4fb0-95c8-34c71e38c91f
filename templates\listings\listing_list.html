{% extends "base.html" %}
{% load static %}

{% block title %}Explore Local Food - TasteLocal{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">Explore Local Culinary Experiences</h1>

    <!-- Search and Filter Form -->
    <form method="get" action="{% url 'listings:listing_list' %}" class="mb-5 p-4 bg-light rounded border">
        <div class="row g-3 align-items-end">
            <div class="col-md-6">
                <label for="searchQuery" class="form-label">Search by Name</label>
                <input type="text" name="q" id="searchQuery" class="form-control" placeholder="e.g., Pizza, Tacos, Food Tour" value="{{ current_query }}">
            </div>
            <div class="col-md-4">
                <label for="categorySelect" class="form-label">Filter by Category</label>
                <select name="category" id="categorySelect" class="form-select">
                    <option value="">All Categories</option>
                    {% for cat in categories %}
                    <option value="{{ cat.slug }}" {% if current_category == cat.slug %}selected{% endif %}>{{ cat.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">Search</button>
            </div>
        </div>
    </form>


    {% if listings %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for listing in listings %}
            <div class="col">
                <div class="card h-100 shadow-sm">
                    {% if listing.image %}
                        <img src="{{ listing.image.url }}" class="card-img-top" alt="{{ listing.title }}" style="height: 220px; object-fit: cover;">
                    {% else %}
                         {# Provide a default placeholder image #}
                         <img src="https://via.placeholder.com/400x220.png?text=TasteLocal+Experience" class="card-img-top" alt="Placeholder Image" style="height: 220px; object-fit: cover;">
                    {% endif %}
                    <div class="card-body d-flex flex-column">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title mb-0">{{ listing.title }}</h5>
                            {% if listing.id in favorited_listing_ids %}
                                <span class="badge bg-danger" title="Favorited"><i class="fas fa-heart"></i></span>
                            {% endif %}
                        </div>
                        <p class="card-text text-muted mb-2">
                            <small>By: <a href="{% url 'listings:vendor_detail' pk=listing.vendor.pk %}" class="text-decoration-none">{{ listing.vendor.name }}</a></small><br>
                            {% if listing.category %}<small>Category: {{ listing.category.name }}</small>{% endif %}
                        </p>
                        <p class="card-text flex-grow-1">{{ listing.description|truncatewords:20 }}</p> {# Allow description to take up space #}

                        {% if listing.average_rating %}
                            <p class="card-text mb-2"><small class="text-warning">★ {{ listing.average_rating|floatformat:1 }} / 5</small></p>
                        {% else %}
                            <p class="card-text mb-2"><small class="text-muted">No reviews yet</small></p>
                        {% endif %}

                        <a href="{% url 'listings:listing_detail' pk=listing.pk %}" class="btn btn-outline-primary mt-auto align-self-start">View Details</a> {# Use mt-auto to push button down #}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
            <nav aria-label="Page navigation" class="mt-5">
                <ul class="pagination justify-content-center">
                    {# Previous Page Link #}
                    {% if page_obj.has_previous %}
                        {# Construct URL preserving existing query parameters #}
                        {% url 'listings:listing_list' as base_url %}
                        <li class="page-item">
                            <a class="page-link" href="{{ base_url }}?page={{ page_obj.previous_page_number }}{% if current_query %}&q={{ current_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span> Previous
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">&laquo; Previous</span></li>
                    {% endif %}

                    {# Page Number Links #}
                    {% for i in paginator.page_range %}
                        {% url 'listings:listing_list' as base_url %}
                        {% if page_obj.number == i %}
                            <li class="page-item active" aria-current="page"><span class="page-link">{{ i }}</span></li>
                        {% else %}
                            <li class="page-item"><a class="page-link" href="{{ base_url }}?page={{ i }}{% if current_query %}&q={{ current_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}

                    {# Next Page Link #}
                    {% if page_obj.has_next %}
                        {% url 'listings:listing_list' as base_url %}
                        <li class="page-item">
                            <a class="page-link" href="{{ base_url }}?page={{ page_obj.next_page_number }}{% if current_query %}&q={{ current_query }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}" aria-label="Next">
                                Next <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                    {% else %}
                        <li class="page-item disabled"><span class="page-link">Next &raquo;</span></li>
                    {% endif %}
                </ul>
            </nav>
        {% endif %} {# End is_paginated check #}

    {% else %}
        <div class="alert alert-info text-center" role="alert">
            No listings found matching your criteria. Try broadening your search!
        </div>
    {% endif %} {# End if listings check #}

</div> {# End container #}
{% endblock %}
