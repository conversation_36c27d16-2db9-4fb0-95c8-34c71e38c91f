# listings/models.py
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MaxValueValidator, MinValueValidator

class Category(models.Model):
    """ Represents categories like Restaurant, Cafe, Food Tour, Street Food Stall """
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=110, unique=True, help_text="URL-friendly version of the name")
    description = models.TextField(blank=True, null=True)

    class Meta:
        verbose_name_plural = "Categories"

    def __str__(self):
        return self.name

class Vendor(models.Model):
    """ Represents the business entity (Restaurant, Cafe, Tour Operator, etc.) """
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='vendors', help_text="The user account managing this vendor profile")
    name = models.CharField(max_length=200)
    description = models.TextField()
    address = models.Char<PERSON>ield(max_length=255)
    phone_number = models.Char<PERSON>ield(max_length=20, blank=True, null=True)
    website = models.URLField(blank=True, null=True)
    opening_hours = models.TextField(blank=True, null=True, help_text="e.g., Mon-Fri 9am-5pm, Sat 10am-3pm")
    # For Geolocation (simple version)
    latitude = models.DecimalField(max_digits=9, decimal_places=6, blank=True, null=True)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, blank=True, null=True)
    # --- ADD THIS FIELD ---
    logo = models.ImageField(
        upload_to='vendors/logos/', # Directory within MEDIA_ROOT
        blank=True,                 # Make it optional
        null=True,
        help_text="Upload a logo or representative image for your business (optional)."
    )
    # --- END OF ADDED FIELD ---
    # Admin approval helps maintain quality (addresses trust need)
    is_approved = models.BooleanField(default=False, help_text="Approved by TasteLocal admin?")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    # Add logo/primary image later if needed
    # logo = models.ImageField(upload_to='vendors/logos/', blank=True, null=True)

    def __str__(self):
        return self.name

class Listing(models.Model):
    """ Represents a specific offering: a dish, a tour, an experience """
    LISTING_TYPE_CHOICES = (
        ('experience', 'Culinary Experience'), # e.g., cooking class, tasting
        ('tour', 'Food Tour'),
        ('dining', 'Dining Spot'), # Represents the vendor itself as a place to visit
        ('product', 'Local Product'), # e.g., special cheese, wine from vendor
    )
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name='listings')
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True, related_name='listings')
    title = models.CharField(max_length=255)
    description = models.TextField()
    listing_type = models.CharField(max_length=20, choices=LISTING_TYPE_CHOICES, default='dining')
    price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, help_text="Price per person/item. Leave blank if not applicable (e.g., free entry).")
    duration_minutes = models.PositiveIntegerField(blank=True, null=True, help_text="Duration in minutes (for tours/experiences)")
    image = models.ImageField(upload_to='listings/images/', blank=True, null=True)
    is_active = models.BooleanField(default=True, help_text="Is this listing currently offered?")
    is_bookable = models.BooleanField(default=False, help_text="Can this listing be booked directly through the platform?")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.title} by {self.vendor.name}"

    @property
    def average_rating(self):
        # Calculate average rating from reviews
        avg = self.reviews.aggregate(models.Avg('rating'))['rating__avg']
        return round(avg, 1) if avg else None

class Review(models.Model):
    """ User reviews for listings """
    listing = models.ForeignKey(Listing, on_delete=models.CASCADE, related_name='reviews')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reviews') # User who wrote the review
    rating = models.PositiveSmallIntegerField(validators=[MinValueValidator(1), MaxValueValidator(5)])
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('listing', 'user') # User can only review a listing once
        ordering = ['-created_at']

    def __str__(self):
        return f"Review for {self.listing.title} by {self.user.username}"

# --- ADD THIS MODEL ---
class VendorQuery(models.Model):
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name='queries')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_queries', help_text="User who sent the query")
    subject = models.CharField(max_length=200)
    message = models.TextField()
    reply = models.TextField(blank=True, null=True, help_text="Vendor's reply to the query")
    created_at = models.DateTimeField(auto_now_add=True)
    replied_at = models.DateTimeField(blank=True, null=True)
    is_read_by_vendor = models.BooleanField(default=False, help_text="Has the vendor seen this query?")
    is_replied = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Vendor Query"
        verbose_name_plural = "Vendor Queries"

    def __str__(self):
        return f"Query from {self.user.username} to {self.vendor.name} re: {self.subject}"
# --- END OF ADDED MODEL ---

class FavoriteListing(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorite_listings')
    listing = models.ForeignKey(Listing, on_delete=models.CASCADE, related_name='favorited_by')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'listing') # A user can only favorite a listing once
        ordering = ['-created_at']
        verbose_name = "Favorite Listing"
        verbose_name_plural = "Favorite Listings"

    def __str__(self):
        return f"{self.user.username} favorited {self.listing.title}"

class FavoriteVendor(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorite_vendors')
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name='favorited_by_users')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'vendor')
        ordering = ['-created_at']
        verbose_name = "Favorite Vendor"
        verbose_name_plural = "Favorite Vendors"

    def __str__(self):
        return f"{self.user.username} favorited {self.vendor.name}"        