# users/admin.py
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import Profile
from bookings.models import Booking
from listings.models import Review, Vendor
from django.db.models import Count
from django.urls import reverse
from django.utils.html import format_html

class ProfileInline(admin.StackedInline):
    model = Profile
    can_delete = False
    verbose_name_plural = 'Profile'
    fk_name = 'user'
    fields = ('user_type', 'phone_number', 'bio') # Control editable fields

class UserAdmin(BaseUserAdmin):
    inlines = (ProfileInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'get_user_type_display', 'get_booking_count', 'get_review_count', 'get_vendor_link')
    list_select_related = ('profile',)
    list_filter = BaseUserAdmin.list_filter + ('profile__user_type',) # Add user_type to filter

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            _booking_count=Count('bookings', distinct=True),
            _review_count=Count('reviews', distinct=True)
        )
        return queryset

    def get_user_type_display(self, instance):
        if hasattr(instance, 'profile'):
            return instance.profile.get_user_type_display()
        return "N/A"
    get_user_type_display.short_description = 'User Type'
    get_user_type_display.admin_order_field = 'profile__user_type'

    def get_booking_count(self, instance):
        return instance._booking_count
    get_booking_count.short_description = 'Bookings Made'
    get_booking_count.admin_order_field = '_booking_count'

    def get_review_count(self, instance):
        return instance._review_count
    get_review_count.short_description = 'Reviews Written'
    get_review_count.admin_order_field = '_review_count'

    def get_vendor_link(self, instance):
        try:
            vendor = Vendor.objects.get(owner=instance)
            link = reverse("admin:listings_vendor_change", args=[vendor.id])
            return format_html('<a href="{}">{}</a>', link, vendor.name)
        except Vendor.DoesNotExist:
            return "-"
    get_vendor_link.short_description = 'Vendor Profile'


admin.site.unregister(User)
admin.site.register(User, UserAdmin)

@admin.register(Profile) # Optionally register Profile separately for direct management
class ProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'user_type', 'phone_number')
    search_fields = ('user__username', 'phone_number')
    list_filter = ('user_type',)
    list_editable = ('user_type',) # Allow direct editing of user_type here
