# tastelocal_project/urls.py
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from users.views import CustomLoginView
from django.contrib.auth import views as auth_views
from tastelocal_project.admin import tastelocal_admin_site  # Import your custom admin site
# --- IMPORT THE NEW VIEW ---
from listings.views import about_us_view , contact_page_view, privacy_policy_view # <-- ADD NEW VIEWS

urlpatterns = [
    # path('admin/', admin.site.urls), # Comment out or remove default admin URL
    path('admin/', tastelocal_admin_site.urls), # <-- USE YOUR CUSTOM ADMIN SITE URLS
    path('accounts/login/', CustomLoginView.as_view(), name='login'),
 
   # Keep other default authentication URLs if required
    path('accounts/password_change/', auth_views.PasswordChangeView.as_view(template_name='registration/password_change_form.html'), name='password_change'), # Specify template if needed
    path('accounts/password_change/done/', auth_views.PasswordChangeDoneView.as_view(template_name='registration/password_change_done.html'), name='password_change_done'), # Specify template if needed
    path('accounts/password_reset/', auth_views.PasswordResetView.as_view(template_name='registration/password_reset_form.html'), name='password_reset'), # Specify template if needed
    path('accounts/password_reset/done/', auth_views.PasswordResetDoneView.as_view(template_name='registration/password_reset_done.html'), name='password_reset_done'), # Specify template if needed
    path('accounts/reset/<uidb64>/<token>/', auth_views.PasswordResetConfirmView.as_view(template_name='registration/password_reset_confirm.html'), name='password_reset_confirm'), # Specify template if needed
    path('accounts/reset/done/', auth_views.PasswordResetCompleteView.as_view(template_name='registration/password_reset_complete.html'), name='password_reset_complete'), # Specify template if needed


    path('accounts/', include('django.contrib.auth.urls')), # Standard auth URLs (login, logout, etc.)
    path('users/', include('users.urls')), # Your custom user URLs (like registration)
    path('bookings/', include('bookings.urls')),
    path('', include('listings.urls')), # Include listings app URLs at root
    # --- ADD URL FOR ABOUT US ---
    path('about/', about_us_view, name='about_us'),
    # --- END ABOUT US URL ---

    # --- ADD URLS FOR CONTACT & PRIVACY POLICY ---
    path('contact-info/', contact_page_view, name='contact_page'), # Using 'contact_page' to distinguish
    path('privacy-policy/', privacy_policy_view, name='privacy_policy'),
    # --- END CONTACT & PRIVACY POLICY URLS ---
]

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT) # If using STATIC_ROOT
