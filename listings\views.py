# listings/views.py
from django.shortcuts import render, get_object_or_404, redirect
from django.views import generic
from bookings.models import Booking
from django.contrib.auth.decorators import login_required, user_passes_test # <-- Import user_passes_test
from django.contrib import messages
from django.urls import reverse
from .models import Listing, Vendor, Review, Category, VendorQuery , FavoriteListing ,FavoriteVendor # Add FavoriteListing
from bookings.models import Booking, PaymentTransaction # Ensure Booking is imported
from django.db.models import Count, Avg, Prefetch # For aggregation
from .forms import ReviewForm, ListingForm, VendorForm, VendorQueryForm # Add FavoriteListing
from django.contrib.auth.mixins import LoginRequiredMixin # For class-based views
from django.core.exceptions import PermissionDenied
from django.views.decorators.http import require_POST # <-- Import require_POST
from django.utils import timezone
from django.http import JsonResponse # Ensure this is imported
# --- ADD THIS VIEW FOR ABOUT US ---
def about_us_view(request):
    return render(request, 'about_us.html') # Or 'listings/about_us.html' if you placed it there
def contact_page_view(request):
    return render(request, 'contact_page.html') # Or 'listings/contact_page.html'

def privacy_policy_view(request):
    return render(request, 'privacy_policy.html') # Or 'listings/privacy_policy.html'

# --- END ABOUT US VIEW ---
def home(request):
    # Show some featured listings or categories on the homepage
    featured_listings = Listing.objects.filter(
        is_active=True,
        vendor__is_approved=True,
        image__isnull=False # Ensure there's an image
    ).annotate(
        num_reviews=Count('reviews'),
        avg_rating=Avg('reviews__rating')
    ).filter(
        avg_rating__gte=0 # Example: only listings with avg rating >= 4
    ).order_by('title')[:6] # Get up to 6

    categories = Category.objects.annotate(listing_count=Count('listings')).filter(listing_count__gt=0).order_by('name')
    context = {
        'featured_listings': featured_listings,
        'categories': categories,
    }
        # --- ADD FAVORITE LISTING IDS FOR LOGGED-IN USER ---
    if request.user.is_authenticated:
        favorited_listing_ids = FavoriteListing.objects.filter(user=request.user).values_list('listing_id', flat=True)
        context['favorited_listing_ids'] = set(favorited_listing_ids)
    else:
        context['favorited_listing_ids'] = set() # Empty set for anonymous users
    # --- END ADD FAVORITE LISTING IDS ---
    return render(request, 'listings/home.html', context)

class ListingListView(generic.ListView):
    model = Listing
    template_name = 'listings/listing_list.html'
    context_object_name = 'listings'
    paginate_by = 9 # Show 10 listings per page

    def get_queryset(self):
        # Only show active listings from approved vendors
        queryset = Listing.objects.filter(is_active=True, vendor__is_approved=True).select_related('vendor', 'category').order_by('title')
        # Basic Search/Filter Example
        query = self.request.GET.get('q')
        category_slug = self.request.GET.get('category')
        if query:
            queryset = queryset.filter(title__icontains=query) # Simple title search
        if category_slug:
            queryset = queryset.filter(category__slug=category_slug)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all() # For filter dropdown
        context['current_query'] = self.request.GET.get('q', '')
        context['current_category'] = self.request.GET.get('category', '')
        if self.request.user.is_authenticated:
            favorited_listing_ids = FavoriteListing.objects.filter(user=self.request.user).values_list('listing_id', flat=True)
            context['favorited_listing_ids'] = set(favorited_listing_ids)
        else:
            context['favorited_listing_ids'] = set()
        return context

@login_required
def edit_vendor(request, pk):
    """ Allows a logged-in vendor user to edit their own Vendor profile, identified by user pk. """
    # --- SECURITY CHECK: Ensure the logged-in user matches the pk in the URL ---
   

    vendor = get_object_or_404(Vendor, pk=pk)

    if vendor.owner.id != request.user.id:
        # Optional: Log this attempt
        messages.error(request, "You do not have permission to edit this vendor profile.")
        # Redirect to their own profile or dashboard instead
        if hasattr(request.user, 'profile') and request.user.profile.user_type == 'vendor':
            return redirect('listings:vendor_dashboard')
        else:
            return redirect('users:profile') # Or 'listings:home'
        # Or raise PermissionDenied("You cannot edit another user's vendor profile.")
    # --- END SECURITY CHECK ---
    # 1. Verify user is a vendor and get their Vendor instance
    # Use get_object_or_404 for cleaner handling if vendor profile doesn't exist
    # This case should ideally not happen if they reached the dashboard,
    # but handle defensively. Check if they have the vendor profile type.
    if not hasattr(request.user, 'profile') or request.user.profile.user_type != 'vendor':
        messages.error(request, "Your vendor profile seems to be missing. Please contact support.")
     
        return redirect('users:profile') # Redirect to user profile


    # 2. Handle POST request (form submission)
    if request.method == 'POST':
        # Pass instance=vendor to pre-populate and update the existing object
        form = VendorForm(request.POST, request.FILES, instance=vendor)
        if form.is_valid():
            form.save() # Updates the existing vendor instance
            messages.success(request, f"Vendor profile '{vendor.name}' updated successfully!")
            return redirect('listings:vendor_dashboard') # Redirect back to the dashboard
        else:
            messages.error(request, "Please correct the errors below.")
    # 3. Handle GET request (initial page load)
    else:
        # Pass instance=vendor to pre-populate the form with current data
        form = VendorForm(instance=vendor)

    # 4. Prepare context and render template
    context = {
        'form': form,
        'vendor': vendor # Pass vendor for context if template needs it (e.g., title)
    }
    # Reuse the vendor_form template
    return render(request, 'listings/vendor_form.html', context)

class ListingDetailView(generic.DetailView):
    model = Listing
    template_name = 'listings/listing_detail.html'
    context_object_name = 'listing'

    def get_queryset(self):
         # Ensure only active/approved listings are viewable directly
        return Listing.objects.filter(is_active=True, vendor__is_approved=True).select_related('vendor')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['reviews'] = Review.objects.filter(listing=self.object).select_related('user').order_by('-created_at')
        if self.request.user.is_authenticated:
            context['review_form'] = ReviewForm()
            context['user_review'] = Review.objects.filter(listing=self.object, user=self.request.user).first()
            # --- ADD FAVORITE STATUS ---
            context['is_favorited'] = FavoriteListing.objects.filter(user=self.request.user, listing=self.object).exists()
            # --- END FAVORITE STATUS ---
        else:
            context['is_favorited'] = False
        return context

class VendorDetailView(generic.DetailView):
    model = Vendor
    template_name = 'listings/vendor_detail.html'
    context_object_name = 'vendor'

    def get_queryset(self):
        # Only show approved vendors
        return Vendor.objects.filter(is_approved=True)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        vendor_obj = self.object
        context['listings'] = Listing.objects.filter(vendor=vendor_obj, is_active=True).select_related('category')
        if self.request.user.is_authenticated:
            context['query_form'] = VendorQueryForm()
            # --- ADD VENDOR FAVORITE STATUS ---
            context['is_favorited_vendor'] = FavoriteVendor.objects.filter(user=self.request.user, vendor=vendor_obj).exists()
            # --- For listings on this page ---
            favorited_listing_ids = FavoriteListing.objects.filter(user=self.request.user).values_list('listing_id', flat=True)
            context['favorited_listing_ids'] = set(favorited_listing_ids)
        else:
            context['is_favorited_vendor'] = False
            context['favorited_listing_ids'] = set()
        return context

@login_required
def add_review(request, listing_id):
    listing = get_object_or_404(Listing, pk=listing_id, is_active=True, vendor__is_approved=True)
    # Prevent user from reviewing multiple times
    if Review.objects.filter(listing=listing, user=request.user).exists():
         messages.error(request, "You have already reviewed this listing.")
         return redirect('listings:listing_detail', pk=listing.id)

    if request.method == 'POST':
        form = ReviewForm(request.POST)
        if form.is_valid():
            review = form.save(commit=False)
            review.listing = listing
            review.user = request.user
            review.save()
            messages.success(request, "Your review has been submitted!")
            return redirect('listings:listing_detail', pk=listing.id)
    else:
        # Should not happen if form is only shown on detail page, but handle anyway
         messages.error(request, "Invalid request method.")
         return redirect('listings:listing_detail', pk=listing.id)
    # If form invalid, redirect back with error (form errors handled by template)
    messages.error(request, "There was an issue with your review. Please verify the form and try again.")
    # Redirecting to the listing detail page for simplicity.
    return redirect('listings:listing_detail', pk=listing.id)


# --- Vendor Specific Views ---@login_required
def vendor_dashboard(request):
    # Ensure user is a vendor
    if not hasattr(request.user, 'profile') or request.user.profile.user_type != 'vendor':
         messages.error(request, "You do not have permission to access the vendor dashboard.")
         return redirect('listings:home')

    try:
        vendor = Vendor.objects.get(owner=request.user)
    except Vendor.DoesNotExist:
         messages.info(request, "You need to create your vendor profile first.")
         return redirect('listings:home')

    vendor_listings_queryset = Listing.objects.filter(vendor=vendor)
    vendor_listings = vendor_listings_queryset.order_by('-created_at')
    vendor_bookings_for_management = Booking.objects.filter(listing__vendor=vendor).select_related('listing', 'user').prefetch_related('payment_transaction').order_by('status', 'booking_date')
    # --- Fetch Queries for this Vendor ---
    vendor_queries = VendorQuery.objects.filter(vendor=vendor).select_related('user').order_by('is_replied', '-created_at') # Unreplied first

    # --- Basic Analytics Data ---
    analytics_data = {}
    analytics_data['total_listings'] = vendor_listings_queryset.count()

    all_vendor_bookings = Booking.objects.filter(listing__vendor=vendor) # Used for analytics
    analytics_data['total_bookings_all_statuses'] = all_vendor_bookings.count()
    analytics_data['pending_bookings'] = all_vendor_bookings.filter(status='pending').count()
    analytics_data['confirmed_bookings'] = all_vendor_bookings.filter(status='confirmed').count()
    analytics_data['completed_bookings'] = all_vendor_bookings.filter(status='completed').count()

    all_vendor_reviews = Review.objects.filter(listing__vendor=vendor)
    analytics_data['total_reviews'] = all_vendor_reviews.count()
    average_rating_data = all_vendor_reviews.aggregate(average=Avg('rating'))
    avg_rating = average_rating_data['average']
    analytics_data['average_rating'] = float(f"{avg_rating:.1f}") if avg_rating is not None else 0.0

    analytics_data['total_listing_views'] = "N/A"
    # --- End Basic Analytics Data ---

    # --- Fetch Queries for this Vendor ---
    vendor_queries = VendorQuery.objects.filter(vendor=vendor)\
                                        .select_related('user')\
                                        .order_by('is_replied', '-created_at')
    # --- End Fetch Queries ---


    context = {
        'vendor': vendor,
        'listings': vendor_listings,
        'bookings': vendor_bookings_for_management,
        'analytics_data': analytics_data,
        'vendor_queries': vendor_queries, # <-- Add queries to context
    }
    return render(request, 'listings/vendor_dashboard.html', context)


@login_required
def add_listing(request):
    if not hasattr(request.user, 'profile') or request.user.profile.user_type != 'vendor':
         messages.error(request, "You must be a registered vendor to add listings.")
         return redirect('listings:home')

    try:
        vendor = Vendor.objects.get(owner=request.user)
    except Vendor.DoesNotExist:
         messages.error(request, "Vendor profile not found for your account.")
         # Ideally redirect to vendor profile creation/setup
         return redirect('listings:vendor_dashboard')

    if request.method == 'POST':
        form = ListingForm(request.POST, request.FILES) # Include request.FILES for image uploads
        if form.is_valid():
            listing = form.save(commit=False)
            listing.vendor = vendor # Assign the vendor automatically
            listing.save()
            messages.success(request, f"Listing '{listing.title}' created successfully!")
            return redirect('listings:vendor_dashboard')
        else:
             messages.error(request, "Please correct the errors below.")
    else:
        form = ListingForm()

    context = {
        'form': form,
        'vendor': vendor
    }
    return render(request, 'listings/listing_form.html', context)

# --- ADD THIS VIEW ---
@login_required
def edit_listing(request, pk):
    """ Allows a vendor to edit their own listing. """
    # 1. Verify user is a vendor
    if not hasattr(request.user, 'profile') or request.user.profile.user_type != 'vendor':
        messages.error(request, "You must be a registered vendor to edit listings.")
        return redirect('listings:home')

    # 2. Get the Vendor associated with the logged-in user
    try:
        vendor = Vendor.objects.get(owner=request.user)
    except Vendor.DoesNotExist:
        messages.error(request, "Vendor profile not found for your account.")
        # Redirect to dashboard as they shouldn't be trying to edit if they have no profile
        return redirect('listings:vendor_dashboard')

    # 3. Get the specific Listing, ensuring it belongs to this vendor
    # This prevents a vendor from editing another vendor's listing via URL manipulation
    listing = get_object_or_404(Listing, pk=pk, vendor=vendor)

    # 4. Handle POST request (form submission)
    if request.method == 'POST':
        # Pass instance=listing to pre-populate and update the existing object
        form = ListingForm(request.POST, request.FILES, instance=listing)
        if form.is_valid():
            form.save() # Updates the existing listing instance
            messages.success(request, f"Listing '{listing.title}' updated successfully!")
            return redirect('listings:vendor_dashboard') # Redirect back to the dashboard
        else:
            # If form is invalid, messages are added and the form is re-rendered below
            messages.error(request, "Please correct the errors below.")
    # 5. Handle GET request (initial page load)
    else:
        # Pass instance=listing to pre-populate the form with current data
        form = ListingForm(instance=listing)

    # 6. Prepare Context for rendering the template
    context = {
        'form': form,
        'vendor': vendor,
        'listing': listing # Pass the listing object itself for context (e.g., template title)
    }
    # 7. Render the same template used for adding a listing
    return render(request, 'listings/listing_form.html', context)
# --- END OF ADDED VIEW ---

# --- ADD THIS VIEW ---
@login_required
def add_vendor(request):
    """ Allows a logged-in user to create a Vendor profile. """
    # 1. Check if user already owns a vendor profile
    if Vendor.objects.filter(owner=request.user).exists():
        messages.warning(request, "You already have a vendor profile associated with your account.")
        return redirect('listings:vendor_dashboard') # Redirect to their existing dashboard

    # 2. Handle POST request (form submission)
    if request.method == 'POST':
        form = VendorForm(request.POST, request.FILES)
        if form.is_valid():
            vendor = form.save(commit=False) # Don't save to DB yet
            vendor.owner = request.user      # Assign the logged-in user
            vendor.is_approved = False       # Set initial approval status
            vendor.save()                    # Now save the complete object
            messages.success(request, f"Vendor profile '{vendor.name}' created successfully! It's pending administrator approval.")
            # Redirect to the vendor dashboard (even though it might show pending status)
            return redirect('listings:vendor_dashboard')
        else:
            messages.error(request, "Please correct the errors below.")
    # 3. Handle GET request (initial page load)
    else:
        form = VendorForm() # Create an empty form

    # 4. Prepare context and render template
    context = {
        'form': form
    }
    # We'll create a new template for this
    return render(request, 'listings/vendor_form.html', context)
# --- END OF ADDED VIEW ---

# --- ADD STAFF VENDOR MANAGEMENT VIEWS ---

def staff_check(user):
    """ Helper function to check if user is staff. """
    return user.is_staff

@login_required
@user_passes_test(staff_check) # Restrict to staff users
@require_POST # Ensure this action is via POST
def staff_approve_vendor(request, vendor_pk):
    vendor = get_object_or_404(Vendor, pk=vendor_pk)
    if not vendor.is_approved:
        vendor.is_approved = True
        vendor.save()
        messages.success(request, f"Vendor '{vendor.name}' has been approved.")
        # Optional: Send notification to vendor owner
    else:
        messages.info(request, f"Vendor '{vendor.name}' is already approved.")
    return redirect('users:profile') # Redirect back to the staff user's profile page

@login_required
@user_passes_test(staff_check) # Restrict to staff users
@require_POST # Ensure this action is via POST
def staff_disapprove_vendor(request, vendor_pk):
    vendor = get_object_or_404(Vendor, pk=vendor_pk)
    if vendor.is_approved:
        vendor.is_approved = False # Set back to pending/not approved
        vendor.save()
        messages.warning(request, f"Vendor '{vendor.name}' has been disapproved and set to pending.")
        # Optional: Send notification to vendor owner
    else:
        messages.info(request, f"Vendor '{vendor.name}' is already not approved (pending).")
    return redirect('users:profile') # Redirect back to the staff user's profile page

# --- END OF STAFF VENDOR MANAGEMENT VIEWS ---

# --- ADD THIS VIEW ---
@login_required # User must be logged in to send a query
@require_POST # This view will only handle POST requests for form submission
def submit_vendor_query(request, vendor_pk):
    vendor = get_object_or_404(Vendor, pk=vendor_pk, is_approved=True) # Ensure vendor is valid
    form = VendorQueryForm(request.POST)

    if form.is_valid():
        query = form.save(commit=False)
        query.vendor = vendor
        query.user = request.user
        query.save()
        messages.success(request, f"Your query regarding '{query.subject}' has been sent to {vendor.name}.")
    else:
        # If form is invalid, collect errors and display them
        # This is a basic way; a more robust way might re-render the vendor_detail page with the form and errors.
        error_message = "There was an error with your query: "
        for field, errors in form.errors.items():
            error_message += f"{field.capitalize()}: {', '.join(errors)} "
        messages.error(request, error_message.strip())

    return redirect('listings:vendor_detail', pk=vendor_pk) # Redirect back to vendor detail page
# --- END OF ADDED VIEW ---

# --- ADD THIS VIEW ---
@login_required
@require_POST # This view handles the form submission for replying
def reply_to_query(request, query_pk):
    # Ensure the logged-in user is a vendor
    if not hasattr(request.user, 'profile') or request.user.profile.user_type != 'vendor':
        messages.error(request, "You do not have permission to reply to queries.")
        return redirect('listings:home') # Or some other appropriate redirect

    vendor = get_object_or_404(Vendor, owner=request.user)
    query = get_object_or_404(VendorQuery, pk=query_pk, vendor=vendor) # Ensure query belongs to this vendor

    reply_message = request.POST.get('reply_message', '').strip()

    if not reply_message:
        messages.error(request, "Reply message cannot be empty.")
    elif query.is_replied:
        messages.warning(request, "This query has already been replied to.")
    else:
        query.reply = reply_message
        query.is_replied = True
        query.replied_at = timezone.now()
        query.is_read_by_vendor = True # Replying implies reading
        query.save()
        messages.success(request, f"Your reply to '{query.subject}' has been sent.")
        # Optional: Send email notification to the user who sent the query

    return redirect('listings:vendor_dashboard') # Redirect back to dashboard
# --- END OF ADDED VIEW ---

@login_required
@require_POST # Ensures this action is via POST for safety
def toggle_favorite_listing(request, listing_pk):
    listing = get_object_or_404(Listing, pk=listing_pk, is_active=True, vendor__is_approved=True)
    
    favorite, created = FavoriteListing.objects.get_or_create(
        user=request.user, 
        listing=listing
    )

    if not created: 
        favorite.delete()
        is_favorited = False
        message = f"'{listing.title}' has been removed from your favorites."
    else:
        is_favorited = True
        message = f"'{listing.title}' has been added to your favorites."
    
    # Check if the request is AJAX
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({'is_favorited': is_favorited, 'message': message, 'listing_pk': listing.pk}) # Added listing_pk for potential use
    
    # Fallback for non-AJAX (e.g., if JS is disabled)
    messages.success(request, message)
    return redirect('listings:listing_detail', pk=listing_pk)

@login_required
@require_POST
def toggle_favorite_vendor(request, vendor_pk):
    vendor = get_object_or_404(Vendor, pk=vendor_pk, is_approved=True)
    
    favorite, created = FavoriteVendor.objects.get_or_create(
        user=request.user,
        vendor=vendor
    )

    if not created:
        # It existed, so we delete it (unfavorite)
        favorite.delete()
        is_favorited_vendor = False
        message = f"'{vendor.name}' has been removed from your favorite vendors."
    else:
        # It was created, so it's now favorited
        is_favorited_vendor = True
        message = f"'{vendor.name}' has been added to your favorite vendors."

    # For AJAX requests, return JSON
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({'is_favorited_vendor': is_favorited_vendor, 'message': message})

    # For non-AJAX requests (fallback or if JS is disabled)
    messages.success(request, message)
    return redirect('listings:vendor_detail', pk=vendor_pk)