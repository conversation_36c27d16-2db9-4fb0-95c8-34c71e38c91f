# listings/forms.py
from django import forms
from .models import Review, Listing, Vendor,VendorQuery 

class ReviewForm(forms.ModelForm):
    class Meta:
        model = Review
        fields = ['rating', 'comment']
        widgets = {
            'rating': forms.Select(choices=[(i, f"{i} Star{'s' if i > 1 else ''}") for i in range(1, 6)]),
            'comment': forms.Textarea(attrs={'rows': 4}),
        }

class ListingForm(forms.ModelForm):
     class Meta:
        model = Listing
        # Exclude fields automatically set (vendor, created_at, etc.)
        # Exclude is_active/is_bookable initially? Or allow vendor control? Let's allow.
        fields = [
            'title', 'description', 'category', 'listing_type',
            'price', 'duration_minutes', 'image', 'is_active', 'is_bookable'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 5}),
            # Add widgets for better UX if needed (e.g., NumberInput for price/duration)
        }
        help_texts = {
            'price': 'Per person/item. Leave blank if not applicable.',
            'duration_minutes': 'For tours/experiences (in minutes).',
            'image': 'Upload an attractive image for your listing.',
        }
     # Add clean methods for validation if needed
# --- Add VendorForm ---
class VendorForm(forms.ModelForm):
    class Meta:
        model = Vendor
        fields = [
            'name', 'description', 'address', 'phone_number',
            'website', 'opening_hours', 'latitude', 'longitude',
            'logo'
        ]
        widgets = {
            # Add/Ensure 'form-control' class for text/url/tel inputs
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'address': forms.TextInput(attrs={'class': 'form-control'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-control', 'type': 'tel'}), # Use type=tel
            'website': forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://...'}),

            # Textareas
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'opening_hours': forms.Textarea(attrs={'rows': 3, 'placeholder': 'e.g., Mon-Fri 9am-5pm, Sat 10am-3pm', 'class': 'form-control'}),

            # Number inputs
            'latitude': forms.NumberInput(attrs={'step': '0.000001', 'class': 'form-control'}),
            'longitude': forms.NumberInput(attrs={'step': '0.000001', 'class': 'form-control'}),

            # File input (ClearableFileInput handles its structure)
            'logo': forms.ClearableFileInput(attrs={'accept': 'image/*', 'class': 'form-control'}), # Add class here too
        }
        help_texts = {
            'website': 'Optional. Enter the full URL (e.g., https://www.example.com)',
            'latitude': 'Optional. Get from Google Maps (right-click -> "What\'s here?").',
            'longitude': 'Optional. Get from Google Maps (right-click -> "What\'s here?").',
            'logo': 'Optional. Upload a logo or representative image (JPG, PNG, etc.).',
        }
        labels = {
            'name': 'Business Name',
            'description': 'Business Description',
            'opening_hours': 'Opening Hours (Optional)',
            'logo': 'Business Logo/Image',
        }

        # --- ADD VendorQueryForm ---
class VendorQueryForm(forms.ModelForm):
    class Meta:
        model = VendorQuery
        fields = ['subject', 'message'] # Only these fields are filled by the user
        widgets = {
            'subject': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., Question about opening hours'}),
            'message': forms.Textarea(attrs={'class': 'form-control', 'rows': 5, 'placeholder': 'Your detailed question or message...'}),
        }
        labels = {
            'subject': 'Subject of your query',
            'message': 'Your Message',
        }
# --- END VendorQueryForm ---