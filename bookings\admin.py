# bookings/admin.py
from django.contrib import admin
from .models import Booking, PaymentTransaction
from django.urls import reverse
from django.utils.html import format_html

@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    list_display = ('id', 'listing_link', 'user_link', 'booking_date', 'number_of_people', 'status', 'total_price_display', 'payment_status_display', 'created_at')
    list_filter = ('status', 'booking_date', 'created_at', 'listing__vendor')
    search_fields = ('user__username', 'listing__title', 'listing__vendor__name')
    readonly_fields = ('total_price', 'created_at', 'updated_at', 'user', 'listing') # Make user/listing read-only after creation
    list_editable = ('status',)
    date_hierarchy = 'booking_date'
    list_select_related = ('listing', 'user', 'payment_transaction') # Optimize queries

    def listing_link(self, obj):
        link = reverse("admin:listings_listing_change", args=[obj.listing.id])
        return format_html('<a href="{}">{}</a>', link, obj.listing.title)
    listing_link.short_description = 'Listing'

    def user_link(self, obj):
        link = reverse("admin:auth_user_change", args=[obj.user.id])
        return format_html('<a href="{}">{}</a>', link, obj.user.username)
    user_link.short_description = 'User'

    def total_price_display(self, obj):
        return f"${obj.total_price:.2f}" if obj.total_price is not None else "N/A"
    total_price_display.short_description = 'Total Price'

    def payment_status_display(self, obj):
        if hasattr(obj, 'payment_transaction') and obj.payment_transaction:
            return format_html('<span style="color: green;">Paid (${})</span>', obj.payment_transaction.amount_paid)
        elif obj.total_price is not None and obj.total_price > 0 and obj.status not in ['cancelled']:
            return format_html('<span style="color: orange;">Due</span>')
        return "N/A"
    payment_status_display.short_description = 'Payment'


@admin.register(PaymentTransaction)
class PaymentTransactionAdmin(admin.ModelAdmin):
    list_display = ('id', 'booking_link', 'amount_paid', 'payment_status', 'transaction_timestamp_display')
    list_filter = ('payment_status', 'transaction_timestamp')
    search_fields = ('booking__id', 'booking__user__username', 'booking__listing__title')
    readonly_fields = ('booking', 'card_number_dummy', 'expiry_date_dummy', 'cvc_dummy', 'amount_paid', 'transaction_timestamp', 'payment_status')

    def booking_link(self, obj):
        link = reverse("admin:bookings_booking_change", args=[obj.booking.id])
        return format_html('<a href="{}">Booking #{}</a>', link, obj.booking.id)
    booking_link.short_description = 'Booking'

    def transaction_timestamp_display(self, obj):
        return obj.transaction_timestamp.strftime("%Y-%m-%d %H:%M")
    transaction_timestamp_display.short_description = 'Timestamp'
    transaction_timestamp_display.admin_order_field = 'transaction_timestamp'

    def has_add_permission(self, request):
        return False
    def has_change_permission(self, request, obj=None):
        return False
