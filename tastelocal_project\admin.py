# tastelocal_project/admin.py
from django.contrib.admin import AdminSite
from django.urls import path
from django.shortcuts import render
from django.db.models import Count, Sum, Avg
from django.contrib.auth.models import User
from listings.models import Vendor, Listing, Review, Category, VendorQuery # <-- Import VendorQuery
from bookings.models import Booking, PaymentTransaction
from users.models import Profile # Assuming Profile is in users.models
from django.utils import timezone
from datetime import timedelta

class TasteLocalAdminSite(AdminSite):
    site_header = "TasteLocal Administration"
    site_title = "TasteLocal Admin Portal"
    index_title = "Welcome to TasteLocal Dashboard" # This will be used as the main H1 title

    def get_urls(self):
        urls = super().get_urls()
        # You can add custom admin views here if needed later
        return urls

    def index(self, request, extra_context=None):
        """
        Display the main admin index page with custom dashboard data.
        """
        # User Statistics
        user_stats = {
            'total_users': User.objects.count(),
            'staff_users': User.objects.filter(is_staff=True).count(),
            'active_users_last_30_days': User.objects.filter(last_login__gte=timezone.now() - timedelta(days=30)).count(),
        }
        profile_type_counts = Profile.objects.values('user_type').annotate(count=Count('id')).order_by('-count')
        # Create a dictionary for easy lookup of display names
        profile_type_choices_dict = dict(Profile.USER_TYPE_CHOICES)
        user_stats_by_type_display = {}
        for item in profile_type_counts:
            display_name = profile_type_choices_dict.get(item['user_type'], item['user_type'].capitalize())
            user_stats_by_type_display[display_name] = item['count']
        user_stats['by_type'] = user_stats_by_type_display


        # Vendor Statistics
        vendor_stats = {
            'total_vendors': Vendor.objects.count(),
            'approved_vendors': Vendor.objects.filter(is_approved=True).count(),
            'pending_vendors': Vendor.objects.filter(is_approved=False).count(),
        }

        # Listing Statistics
        listing_stats = {
            'total_listings': Listing.objects.count(),
            'active_listings': Listing.objects.filter(is_active=True).count(),
            'bookable_listings': Listing.objects.filter(is_bookable=True).count(),
        }
        top_categories = Category.objects.annotate(num_listings=Count('listings')).order_by('-num_listings')[:5]
        listing_stats['top_categories'] = top_categories


        # Booking Statistics
        booking_stats = {
            'total_bookings': Booking.objects.count(),
        }
        booking_status_counts = Booking.objects.values('status').annotate(count=Count('id')).order_by('-count')
        booking_status_choices_dict = dict(Booking.STATUS_CHOICES)
        booking_stats_by_status_display = {}
        for item in booking_status_counts:
            display_name = booking_status_choices_dict.get(item['status'], item['status'].capitalize())
            booking_stats_by_status_display[display_name] = item['count']
        booking_stats['by_status'] = booking_stats_by_status_display

        total_revenue_data = PaymentTransaction.objects.filter(payment_status='simulated_success').aggregate(total=Sum('amount_paid'))
        booking_stats['total_revenue'] = total_revenue_data['total'] or 0.00


        # Review Statistics
        review_stats = {
            'total_reviews': Review.objects.count(),
            'average_rating_all': Review.objects.aggregate(avg_rating=Avg('rating'))['avg_rating'] or 0.0,
        }

        # Prepare the context for the template
        context = {
            **self.each_context(request),  # Base admin context (site_header, site_title, etc.)
            'title': self.index_title,     # For the main H1 tag in the template
            'app_list': self.get_app_list(request), # For the sidebar app navigation
            'user_stats': user_stats,
            'vendor_stats': vendor_stats,
            'listing_stats': listing_stats,
            'booking_stats': booking_stats,
            'review_stats': review_stats,
            **(extra_context or {}),       # Merge any extra_context passed in
        }
        
        request.current_app = self.name # Important for resolving admin URLs in templates

        return render(request, 'admin/custom_dashboard.html', context)

tastelocal_admin_site = TasteLocalAdminSite(name='tastelocal_admin')

# --- IMPORTANT: Model Registration ---
# Ensure your models are registered with 'tastelocal_admin_site'
# and NOT with the default 'admin.site'.
# You'll need to:
# 1. Remove @admin.register(Model) and admin.site.register(Model, ModelAdmin)
#    from your app's admin.py files (listings/admin.py, users/admin.py, bookings/admin.py).
# 2. Import your ModelAdmin classes here and register them with tastelocal_admin_site.

from django.contrib.auth.models import User,Group

# Import ModelAdmin classes from your apps
from listings.admin import CategoryAdmin, VendorAdmin, ListingAdmin, ReviewAdmin,VendorQueryAdmin 
from users.admin import UserAdmin as CustomUserAdmin # Your custom UserAdmin from users/admin.py
from users.admin import ProfileAdmin # Assuming you defined ProfileAdmin in users/admin.py
from bookings.admin import BookingAdmin, PaymentTransactionAdmin

# Register models with the custom admin site
tastelocal_admin_site.register(Category, CategoryAdmin)
tastelocal_admin_site.register(Vendor, VendorAdmin)
tastelocal_admin_site.register(Listing, ListingAdmin)
tastelocal_admin_site.register(Review, ReviewAdmin)
tastelocal_admin_site.register(VendorQuery, VendorQueryAdmin) # <-- ADD THIS LINE

tastelocal_admin_site.register(User, CustomUserAdmin) # Use your custom UserAdmin
from users.models import Profile # Ensure Profile is imported if not already
tastelocal_admin_site.register(Profile, ProfileAdmin)

tastelocal_admin_site.register(Booking, BookingAdmin)
tastelocal_admin_site.register(PaymentTransaction, PaymentTransactionAdmin)

tastelocal_admin_site.register(Group) # Example for Django's Group model
