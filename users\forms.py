# users/forms.py
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import Profile

class UserRegisterForm(UserCreationForm):
    avatar = forms.ImageField(required=False)
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Enter your email'})
    )
        # --- ADD PHONE NUMBER FIELD ---
    phone_number = forms.CharField(
        label="Phone Number", # Will be overridden in __init__ to add "(Required)"
        required=True,        # This makes the field mandatory at the form level
        widget=forms.TextInput(attrs={'type': 'tel', 'class': 'form-control', 'placeholder': 'e.g., +1234567890'})
    )
    # --- END PHONE NUMBER FIELD ---
    first_name = forms.Char<PERSON>ield(
        required=False, # Or True if you want it mandatory
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Your first name (Optional)'})
    )
    last_name = forms.CharField(
        required=False, # Or True if you want it mandatory
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Your last name (Optional)'})
    )


    class Meta(UserCreationForm.Meta):
        model = User
        # Add email, first_name, last_name to the fields inherited from UserCreationForm
        fields = UserCreationForm.Meta.fields + ('email', 'phone_number', 'first_name', 'last_name',)
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make labels more descriptive or add required indicators
        self.fields['email'].label = "Email Address (Required)"
        self.fields['phone_number'].label = "Phone Number (Required)"

        # Ensure all relevant fields have the form-control class for Bootstrap styling
        for field_name in self.fields:
            if field_name not in ['password2']: # password2 help_text is different
                 self.fields[field_name].widget.attrs.update({'class': 'form-control'})
            # Special handling for password confirmation help text if needed
            if field_name == 'password2' and self.fields[field_name].help_text:
                 self.fields[field_name].help_text = self.fields[field_name].help_text.replace('this field', 'password confirmation')


    # No need to override save() if profile creation is handled in the view
    # def save(self, commit=True):
    #     user = super().save(commit=commit)
    #     if commit:
    #         # Save profile data here if you want to encapsulate it in the form
    #         # Profile.objects.create(user=user, phone_number=self.cleaned_data.get('phone_number'))
    #         pass
    #     return user
class ProfileForm(forms.ModelForm):
    avatar = forms.ImageField(label="Change Profile Picture", required=False, widget=forms.ClearableFileInput(attrs={'class': 'form-control mb-2'}))
    class Meta:
        model = Profile
        fields = ['avatar', 'phone_number', 'bio'] # Only allow editing these fields
        # Exclude 'user' and 'user_type' unless you want users to change type
        widgets = {
            'phone_number': forms.TextInput(attrs={'type': 'tel', 'class': 'form-control', 'placeholder': 'e.g., +1234567890'}),
            'bio': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make phone_number required
        self.fields['phone_number'].required = True
        # Optionally, you can update the label to indicate it's required
        self.fields['phone_number'].label = "Phone Number (Required)"
        # self.fields['phone_number'].help_text = "Please enter your contact phone number."
class UserAndProfileEditForm(forms.ModelForm):
    # Fields from User model
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={'class': 'form-control'})
    )
    first_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    last_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )

    # Fields from Profile model
    phone_number = forms.CharField(
        required=True, # As per previous request
        widget=forms.TextInput(attrs={'type': 'tel', 'class': 'form-control', 'placeholder': 'e.g., +1234567890'})
    )
    bio = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'rows': 3, 'class': 'form-control'})
    )
    # ... other fields ...
    avatar = forms.ImageField(
        label="Change Profile Picture",
        required=False,
        widget=forms.ClearableFileInput(attrs={'class': 'form-control mb-2'})) # Field definition
    # ...
    class Meta:
        model = User # Primary model for the form (can be User or Profile)
        # We list fields here primarily for Django's internal use if it needs a base model.
        # The actual fields rendered are defined above.
        fields = ['email', 'first_name', 'last_name']

    def __init__(self, *args, **kwargs):
        # We need to pop 'instance_profile' before calling super if we pass it
        # For now, we'll assume the view handles passing initial data correctly.
        # If you pass 'instance' for User and 'instance_profile' for Profile:
        profile_instance = kwargs.pop('instance_profile', None)
        super().__init__(*args, **kwargs) # Initializes User model fields if instance is User

        # Set labels and initial values
        self.fields['email'].label = "Email Address (Required)"
        self.fields['first_name'].label = "First Name"
        self.fields['last_name'].label = "Last Name"
        self.fields['phone_number'].label = "Phone Number (Required)"
        self.fields['bio'].label = "Bio / About Me"
        self.fields['avatar'].label = "Profile Picture"
        # Populate initial values if instances are provided
        if self.instance and self.instance.pk: # For User model fields
            self.fields['email'].initial = self.instance.email
            self.fields['first_name'].initial = self.instance.first_name
            self.fields['last_name'].initial = self.instance.last_name

        if profile_instance and profile_instance.pk: # For Profile model fields
            self.fields['phone_number'].initial = profile_instance.phone_number
            self.fields['bio'].initial = profile_instance.bio
            self.fields['avatar'].initial = profile_instance.avatar # Set initial avatar

    def save(self, commit=True):
        # Save User model part
        user = super().save(commit=False) # Get the User instance
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        if commit:
            user.save()

        # Save Profile model part
        profile = user.profile # Assumes user.profile exists (created by signal or get_or_create)
        profile.phone_number = self.cleaned_data['phone_number']
        profile.bio = self.cleaned_data['bio']


        # Handle avatar update
        # self.cleaned_data['avatar'] will be:
        # - None: if the field was not changed (no new file, clear not checked)
        # - False: if the 'clear' checkbox was checked
        # - UploadedFile object: if a new file was uploaded
        if self.cleaned_data['avatar'] is not None: # Check if there was any interaction
            if self.cleaned_data['avatar'] is False: # 'Clear' checkbox was checked
                profile.avatar = None # Or set to a default avatar path if you prefer
            else: # A new file was uploaded
                profile.avatar = self.cleaned_data['avatar']        
        if commit:
            profile.save()
        
        return user # Return the user instance