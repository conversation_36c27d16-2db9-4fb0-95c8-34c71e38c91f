{% extends "admin/base_site.html" %}
{% load static %}

{% block extrastyle %}
    {{ block.super }}
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/dashboard.css' %}">
    <style>
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        .dashboard-card {
            background-color: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .dashboard-card h3 {
            margin-top: 0;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        .dashboard-card ul {
            list-style: none;
            padding-left: 0;
        }
        .dashboard-card li {
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
        }
        .dashboard-card .value {
            font-weight: bold;
            color: #007bff; /* Primary color for values */
        }
        .dashboard-card .sub-value {
            font-size: 0.9em;
            color: #555;
        }
        .dashboard-card .top-list li {
            padding: 0.3rem 0;
            border-bottom: 1px dashed #eee;
        }
        .dashboard-card .top-list li:last-child {
            border-bottom: none;
        }
    </style>
{% endblock %}

{% block coltype %}colMS{% endblock %}

{% block bodyclass %}{{ block.super }} dashboard{% endblock %}

{% block breadcrumbs %}{% endblock %} {# Remove default breadcrumbs for dashboard #}

{% block content_title %}
    <h1>{{ index_title|default:"Dashboard" }}</h1>
{% endblock %}

{% block content %}
<div id="content-main">
    <p class="lead">Overview of platform activity and performance.</p>

    <div class="dashboard-grid">
        <!-- User Statistics -->
        <div class="dashboard-card">
            <h3><i class="fas fa-users me-2"></i>User Statistics</h3>
            <ul>
                <li>Total Users: <span class="value">{{ user_stats.total_users }}</span></li>
                <li>Staff Users: <span class="value">{{ user_stats.staff_users }}</span></li>
                <li>Active (Last 30 Days): <span class="value">{{ user_stats.active_users_last_30_days }}</span></li>
                {% for type, count in user_stats.by_type.items %}
                    <li>{{ type|capfirst }}s: <span class="value">{{ count }}</span></li>
                {% endfor %}
            </ul>
        </div>

        <!-- Vendor Statistics -->
        <div class="dashboard-card">
            <h3><i class="fas fa-store me-2"></i>Vendor Statistics</h3>
            <ul>
                <li>Total Vendors: <span class="value">{{ vendor_stats.total_vendors }}</span></li>
                <li>Approved Vendors: <span class="value text-success">{{ vendor_stats.approved_vendors }}</span></li>
                <li>Pending Approval: <span class="value text-warning">{{ vendor_stats.pending_vendors }}</span></li>
            </ul>
        </div>

        <!-- Listing Statistics -->
        <div class="dashboard-card">
            <h3><i class="fas fa-list-alt me-2"></i>Listing Statistics</h3>
            <ul>
                <li>Total Listings: <span class="value">{{ listing_stats.total_listings }}</span></li>
                <li>Active Listings: <span class="value">{{ listing_stats.active_listings }}</span></li>
                <li>Bookable Listings: <span class="value">{{ listing_stats.bookable_listings }}</span></li>
            </ul>
            {% if listing_stats.top_categories %}
            <h4 class="mt-3 mb-2" style="font-size: 1rem;">Top Categories (by Listings)</h4>
            <ul class="top-list">
                {% for category in listing_stats.top_categories %}
                <li>{{ category.name }} <span class="sub-value">({{ category.num_listings }} listings)</span></li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>

        <!-- Booking Statistics -->
        <div class="dashboard-card">
            <h3><i class="fas fa-calendar-check me-2"></i>Booking Statistics</h3>
            <ul>
                <li>Total Bookings: <span class="value">{{ booking_stats.total_bookings }}</span></li>
                {% for status, count in booking_stats.by_status.items %}
                    <li>{{ status|capfirst }} Bookings: <span class="value">{{ count }}</span></li>
                {% endfor %}
                <li>Total Revenue (Simulated): <span class="value text-success">${{ booking_stats.total_revenue|floatformat:2 }}</span></li>
            </ul>
        </div>

        <!-- Review Statistics -->
        <div class="dashboard-card">
            <h3><i class="fas fa-star me-2"></i>Review Statistics</h3>
            <ul>
                <li>Total Reviews: <span class="value">{{ review_stats.total_reviews }}</span></li>
                <li>Overall Average Rating:
                    <span class="value text-warning">
                        {{ review_stats.average_rating_all|floatformat:1 }} / 5
                    </span>
                </li>
            </ul>
        </div>
    </div>

    <hr class="my-4">
    <p>For detailed management, please use the navigation links on the left or below.</p>

    {# You can include the default app list here if you want #}
    {# {% include "admin/app_list.html" with app_list=app_list show_changelinks=True %} #}

</div>
{% endblock %}
