# Generated by Django 5.1.6 on 2025-05-08 13:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('listings', '0003_vendorquery'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FavoriteListing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('listing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='listings.listing')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorite_listings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Favorite Listing',
                'verbose_name_plural': 'Favorite Listings',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'listing')},
            },
        ),
    ]
