{% extends "base.html" %}
{% load static %}

{% block title %}{% if form.instance.pk %}Edit Listing{% else %}Add New Listing{% endif %} - TasteLocal{% endblock %}

{% block content %}
<div class="container mt-4 mb-5"> {# Added mb-5 for bottom spacing #}
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8"> {# Slightly wider column #}

            {# Optional Breadcrumbs #}
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                  <li class="breadcrumb-item"><a href="{% url 'listings:home' %}">Home</a></li>
                  <li class="breadcrumb-item"><a href="{% url 'listings:vendor_dashboard' %}">Vendor Dashboard</a></li>
                  <li class="breadcrumb-item active" aria-current="page">{% if form.instance.pk %}Edit Listing{% else %}Add Listing{% endif %}</li>
                </ol>
            </nav>

            <div class="card shadow-lg"> {# Added shadow-lg for more emphasis #}
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title mb-0">
                        <i class="fas {% if form.instance.pk %}fa-edit{% else %}fa-plus-circle{% endif %} me-2"></i> {# Icon based on add/edit #}
                        {% if form.instance.pk %}Edit Listing: {{ form.instance.title }}{% else %}Add a New Listing{% endif %}
                    </h2>
                </div>
                <div class="card-body p-4">
                    {% if vendor %}
                    <p class="text-muted border-bottom pb-2 mb-3">Adding listing for: <strong>{{ vendor.name }}</strong></p>
                    {% endif %}

                    {# IMPORTANT: enctype is needed for file uploads #}
                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}

                        {# Display Non-Field Errors #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        {# --- Basic Details Section --- #}
                        <h5 class="text-primary mb-3"><i class="fas fa-info-circle me-2"></i>Basic Details</h5>
                        {# Title #}
                        <div class="mb-3">
                            {% with field=form.title %}
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                <input type="{{ field.field.widget.input_type }}"
                                       name="{{ field.name }}"
                                       id="{{ field.id_for_label }}"
                                       class="form-control {% if field.errors %}is-invalid{% endif %}"
                                       value="{{ field.value|default:'' }}"
                                       required>
                                {% if field.errors %}
                                    <div class="invalid-feedback">{{ field.errors|striptags }}</div>
                                {% endif %}
                                {% if field.help_text %}
                                    <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                {% endif %}
                            {% endwith %}
                        </div>
                        {# Description #}
                        <div class="mb-3">
                             {% with field=form.description %}
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                <textarea name="{{ field.name }}"
                                          id="{{ field.id_for_label }}"
                                          class="form-control {% if field.errors %}is-invalid{% endif %}"
                                          rows="5" required>{{ field.value|default:'' }}</textarea>
                                {% if field.errors %}
                                    <div class="invalid-feedback">{{ field.errors|striptags }}</div>
                                {% endif %}
                                {% if field.help_text %}
                                    <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                {% endif %}
                            {% endwith %}
                        </div>
                        {# Category & Type (Side-by-side) #}
                        <div class="row mb-3">
                            <div class="col-md-6">
                                {% with field=form.category %}
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    {{ field.as_widget }} {# Renders the select widget #}
                                    {% if field.errors %}
                                        <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                    {% endif %}
                                    {% if field.help_text %}
                                        <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                            <div class="col-md-6">
                                {% with field=form.listing_type %}
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    {{ field.as_widget }} {# Renders the select widget #}
                                    {% if field.errors %}
                                        <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                    {% endif %}
                                    {% if field.help_text %}
                                        <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                        </div>

                        <hr class="my-4">

                        {# --- Pricing & Duration Section --- #}
                        <h5 class="text-success mb-3"><i class="fas fa-dollar-sign me-2"></i>Pricing & Duration</h5>
                        <div class="row mb-3">
                            {# Price #}
                            <div class="col-md-6">
                                {% with field=form.price %}
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    <div class="input-group {% if field.errors %}has-validation{% endif %}">
                                        <span class="input-group-text bg-light"><i class="fas fa-dollar-sign text-success"></i></span>
                                        <input type="{{ field.field.widget.input_type }}"
                                               name="{{ field.name }}"
                                               id="{{ field.id_for_label }}"
                                               class="form-control {% if field.errors %}is-invalid{% endif %}"
                                               value="{{ field.value|default:'' }}"
                                               step="0.01" min="0"> {# Attributes for number input #}
                                        {% if field.errors %}
                                            <div class="invalid-feedback">{{ field.errors|striptags }}</div>
                                        {% endif %}
                                    </div>
                                    {% if field.help_text %}
                                        <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                            {# Duration #}
                            <div class="col-md-6">
                                {% with field=form.duration_minutes %}
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    <div class="input-group {% if field.errors %}has-validation{% endif %}">
                                         <span class="input-group-text bg-light"><i class="fas fa-clock text-info"></i></span>
                                         <input type="{{ field.field.widget.input_type }}"
                                               name="{{ field.name }}"
                                               id="{{ field.id_for_label }}"
                                               class="form-control {% if field.errors %}is-invalid{% endif %}"
                                               value="{{ field.value|default:'' }}"
                                               step="1" min="0"> {# Attributes for number input #}
                                        <span class="input-group-text bg-light">minutes</span>
                                        {% if field.errors %}
                                            <div class="invalid-feedback">{{ field.errors|striptags }}</div>
                                        {% endif %}
                                    </div>
                                    {% if field.help_text %}
                                        <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                        </div>

                        <hr class="my-4">

                        {# --- Image Upload Section --- #}
                         <h5 class="text-info mb-3"><i class="fas fa-image me-2"></i>Listing Image</h5>
                         <div class="mb-3 p-3 border bg-light rounded"> {# Added background/border #}
                            {% with field=form.image %}
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                <input type="file"
                                       name="{{ field.name }}"
                                       id="{{ field.id_for_label }}"
                                       class="form-control {% if field.errors %}is-invalid{% endif %}"
                                       accept="image/*">
                                {% if field.errors %}
                                    <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                {% endif %}
                                {% if field.help_text %}
                                    <div class="form-text text-muted mt-1"><small>{{ field.help_text|safe }}</small></div>
                                {% endif %}
                                {# Show current image if editing and one exists #}
                                {% if form.instance.pk and field.value %}
                                    <div class="mt-3">
                                        <small class="d-block text-muted">Current image:</small>
                                        <a href="{{ field.value.url }}" target="_blank">
                                            <img src="{{ field.value.url }}" alt="Current {{ field.label }}" class="img-thumbnail" style="max-height: 70px;"> {# Added img-thumbnail #}
                                        </a>
                                    </div>
                                {% endif %}
                                {# Image preview placeholder #}
                                <div id="imagePreviewContainer" class="mt-2"></div>
                            {% endwith %}
                        </div>

                        <hr class="my-4">

                        {# --- Status Section --- #}
                        <h5 class="text-secondary mb-3"><i class="fas fa-toggle-on me-2"></i>Status & Availability</h5>
                        <div class="row mb-3">
                            {# Is Active #}
                            <div class="col-md-6">
                                {% with field=form.is_active %}
                                    <div class="form-check form-switch form-switch-lg mb-2"> {# Larger switch #}
                                        <input type="checkbox"
                                               name="{{ field.name }}"
                                               id="{{ field.id_for_label }}"
                                               class="form-check-input {% if field.errors %}is-invalid{% endif %}"
                                               role="switch"
                                               {% if field.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    </div>
                                    {% if field.errors %}
                                        <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                    {% endif %}
                                    {% if field.help_text %}
                                        <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                             {# Is Bookable #}
                            <div class="col-md-6">
                                {% with field=form.is_bookable %}
                                     <div class="form-check form-switch form-switch-lg mb-2"> {# Larger switch #}
                                        <input type="checkbox"
                                               name="{{ field.name }}"
                                               id="{{ field.id_for_label }}"
                                               class="form-check-input {% if field.errors %}is-invalid{% endif %}"
                                               role="switch"
                                               {% if field.value %}checked{% endif %}>
                                        <label class="form-check-label" for="{{ field.id_for_label }}">{{ field.label }}</label>
                                    </div>
                                    {% if field.errors %}
                                        <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                    {% endif %}
                                    {% if field.help_text %}
                                        <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                        </div>

                        {# --- Buttons --- #}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end border-top pt-4 mt-4">
                            <a href="{% url 'listings:vendor_dashboard' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas {% if form.instance.pk %}fa-save{% else %}fa-plus{% endif %} me-2"></i>
                                {% if form.instance.pk %}Save Changes{% else %}Add Listing{% endif %}
                            </button>
                        </div>
                    </form>
                </div> {# End card-body #}
            </div> {# End card #}
        </div> {# End col #}
    </div> {# End row #}
</div> {# End container #}
{% endblock %}

{% block extra_scripts %}
{# Font Awesome (if not already in base.html) #}
<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

{# Improved image preview for file input #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('{{ form.image.id_for_label }}'); // Use the actual ID
    const previewContainer = document.getElementById('imagePreviewContainer');

    if (imageInput && previewContainer) {
        imageInput.onchange = evt => {
            // Clear previous preview
            previewContainer.innerHTML = '';

            const [file] = imageInput.files;
            if (file) {
                const preview = document.createElement('img');
                preview.src = URL.createObjectURL(file);
                preview.alt = 'Image preview';
                preview.className = 'img-thumbnail'; // Add Bootstrap class
                preview.style.maxHeight = '100px';
                preview.style.marginTop = '10px';
                preview.style.display = 'block';
                previewContainer.appendChild(preview);
            }
        }
    }
});
</script>
{% endblock %}
