<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}TasteLocal{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">
    <!-- Optional: Add your custom CSS here -->
    <!-- <link rel="stylesheet" href="{% static 'css/style.css' %}"> -->
    {% block extra_head %}{% endblock %}
  </head>
  <body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
      <div class="container">
        <a class="navbar-brand" href="{% url 'listings:home' %}">TasteLocal</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item">
              <a class="nav-link" href="{% url 'listings:listing_list' %}">Explore Food</a>
            </li>
            <!-- Add other nav items like 'About', 'Contact' -->
          </ul>
          <ul class="navbar-nav ms-auto">
             {% if user.is_authenticated %}
                {% if user.profile.user_type == 'vendor' %}
                 <li class="nav-item">
                    <a class="nav-link" href="{% url 'listings:vendor_dashboard' %}">Vendor Dashboard</a>
                 </li>
                {% endif %}
                <li class="nav-item dropdown">
                  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    {{ user.username }}
                  </a>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                    <li><a class="dropdown-item" href="{% url 'users:profile' %}">My Profile</a></li>
                    <li><a class="dropdown-item" href="{% url 'bookings:user_bookings' %}">My Bookings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form method="post" action="{% url 'logout' %}" style="display: inline;">
                            {% csrf_token %}
                            <button type="submit" class="dropdown-item">Logout</button>
                        </form>
                    </li>
                  </ul>
                </li>
             {% else %}
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'login' %}">Login</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link btn btn-primary btn-sm text-white" href="{% url 'users:register' %}">Register</a>
                </li>
             {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <main class="container">
        {% if messages %}
            <div class="messages">
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block content %}
        <!-- Page specific content goes here -->
        {% endblock %}
    </main>

    <footer class="mt-5 py-3 bg-light text-center">
        <div class="container">
            <span class="text-muted">© {% now "Y" %} TasteLocal. Promoting Culinary Heritage.</span>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>
    {% block extra_scripts %}{% endblock %}
  </body>
</html>
