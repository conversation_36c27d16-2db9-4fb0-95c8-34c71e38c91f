{% load static %}
<!doctype html>
<html lang="en" data-bs-theme="light"> {# Optional: Set default theme #}
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}TasteLocal{% endblock %}</title>
    {# Favicon #}
    <link rel="icon" href="{% static 'images/favicon.ico' %}">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css" integrity="sha512-xh6O/CkQoPOWDdYTDqeRdPCVd1SpvCA9XXcUnZS2FmJNp1coAFzvtCN9BmamE+4aHK8yyUHUSCcJHgXloTyT2A==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Custom CSS for site-wide styling -->
    <link rel="stylesheet" href="{% static 'css/custom_style.css' %}">

    {% block extra_head %}{% endblock %}

    <style>
      /* Optional: Add minor custom styles directly or in custom_style.css */
      body {
        display: flex;
        flex-direction: column;
        min-height: 100vh; /* Ensure footer stays at bottom */
      }
      main {
        flex-grow: 1; /* Allow main content to take up available space */
      }
      .navbar {
         /* Add a subtle shadow to the navbar */
         box-shadow: 0 2px 4px rgba(0,0,0,.1);
      }
      .footer {
        /* Add a subtle top border */
        border-top: 1px solid #343a40; /* Slightly lighter than bg-dark */
      }
      .footer a {
        color: rgba(255, 255, 255, 0.7); /* Lighter link color for dark footer */
        text-decoration: none;
      }
      .footer a:hover {
        color: #ffffff;
        text-decoration: underline;
      }
    </style>

  </head>
  <body>
    {# Navbar: Using bg-primary for color, navbar-dark for light text/icons #}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4 shadow-sm">
      <div class="container">
        {# Brand with slightly larger font #}
        <a class="navbar-brand fw-bold" href="{% url 'listings:home' %}">
            <i class="fas fa-utensils me-2"></i>TasteLocal
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto mb-2 mb-lg-0">
            <li class="nav-item">
              <a class="nav-link {% if request.resolver_match.url_name == 'listing_list' %}active{% endif %}" href="{% url 'listings:listing_list' %}">
                <i class="fas fa-search me-1"></i>Explore Food
              </a>
            </li>
            {# Add other nav items like 'About', 'Contact' here #}
           
            {# --- ADD THIS LINK FOR ABOUT US --- #}
            <li class="nav-item">
              <a class="nav-link {% if request.resolver_match.url_name == 'about_us' %}active{% endif %}" href="{% url 'about_us' %}">
                <i class="fas fa-info-circle me-1"></i>About Us
              </a>
            </li>
            {# --- END ABOUT US LINK --- #}
            
          </ul>
          <ul class="navbar-nav ms-auto">
             {% if user.is_authenticated %}
                {% if user.profile.user_type == 'vendor' %}
                 <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'vendor_dashboard' %}active{% endif %}" href="{% url 'listings:vendor_dashboard' %}">
                        <i class="fas fa-tachometer-alt me-1"></i>Vendor Dashboard
                    </a>
                 </li>
                {% endif %}
                <li class="nav-item dropdown">
                  <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user-circle me-1"></i>{{ user.username }}
                  </a>
                  <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                    <li><a class="dropdown-item" href="{% url 'users:profile' %}"><i class="fas fa-user-edit fa-fw me-2"></i>My Profile</a></li>
                    <li><a class="dropdown-item" href="{% url 'bookings:user_bookings' %}"><i class="fas fa-calendar-check fa-fw me-2"></i>My Bookings</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <form method="post" action="{% url 'logout' %}" style="display: block;"> {# Use block for full width #}
                            {% csrf_token %}
                            <button type="submit" class="dropdown-item text-danger">
                                <i class="fas fa-sign-out-alt fa-fw me-2"></i>Logout
                            </button>
                        </form>
                    </li>
                  </ul>
                </li>
             {% else %}
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'login' %}active{% endif %}" href="{% url 'login' %}">
                        <i class="fas fa-sign-in-alt me-1"></i>Login
                    </a>
                </li>
                <li class="nav-item ms-2"> {# Added margin start #}
                    <a class="nav-link btn btn-outline-light btn-sm" href="{% url 'users:register' %}">
                        <i class="fas fa-user-plus me-1"></i>Register
                    </a>
                </li>
             {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <main class="container mt-4 mb-5"> {# Added margin bottom to main #}
        {% if messages %}
            <div class="messages mb-3"> {# Added margin bottom to messages #}
                {% for message in messages %}
                    <div class="alert alert-{% if message.tags %}{{ message.tags }}{% else %}info{% endif %} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        {% block content %}
        <!-- Page specific content goes here -->
        {% endblock %}
    </main>

    {# Footer: Using bg-dark for color, text-white/text-muted for contrast #}
    <footer class="footer mt-auto py-4 bg-dark text-white">
        <div class="container">
            <div class="row">
                {# Column 1: Brand/Copyright #}
                <div class="col-md-4 mb-3 mb-md-0">
                    <h5 class="fw-bold"><i class="fas fa-utensils me-2"></i>TasteLocal</h5>
                    <p class="text-muted small">&copy; {% now "Y" %} TasteLocal. All Rights Reserved.<br> Promoting Culinary Heritage.</p>
                </div>

                {# Column 2: Quick Links (Example) #}
                <div class="col-md-4 mb-3 mb-md-0">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'listings:home' %}">Home</a></li>
                        <li><a href="{% url 'listings:listing_list' %}">Explore Food</a></li>
                        {# Add links to About, Contact, Privacy Policy etc. if you create those pages #}
                        <li><a href="{% url 'about_us' %}">About Us</a></li>
                        {# --- ADD THESE LINKS --- #}
                        <li><a href="{% url 'contact_page' %}">Contact Us</a></li>
                        <li><a href="{% url 'privacy_policy' %}">Privacy Policy</a></li>
                        {# --- END ADDED LINKS --- #}
                    </ul>
                </div>

                {# Column 3: Social Media (Example) #}
                <div class="col-md-4">
                    <h5>Connect With Us</h5>
                    {# Replace # with actual social media links #}
                    <a href="#" class="text-white me-3 fs-4"><i class="fab fa-facebook-square"></i></a>
                    <a href="#" class="text-white me-3 fs-4"><i class="fab fa-instagram-square"></i></a>
                    <a href="#" class="text-white me-3 fs-4"><i class="fab fa-twitter-square"></i></a>
                    {# Add more social icons as needed #}
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL" crossorigin="anonymous"></script>

    {# Font Awesome JS (needed for some advanced features, optional otherwise) #}
    {# <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/js/all.min.js" integrity="sha512-naukR7I+Nk6gp7p5TMA4ycgfxaZBJ7MO5iC3Fp6ySQyKFHOGfpkSZkYVWV5R7u7cfAicxanwYQ5D1e17EfJcMA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script> #}

    {% block extra_scripts %}{% endblock %}
  </body>
</html>