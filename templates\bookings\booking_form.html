{% extends "base.html" %}
{% load static %}

{% block title %}Book: {{ listing.title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb">
                  <li class="breadcrumb-item"><a href="{% url 'listings:listing_list' %}">Explore</a></li>
                  <li class="breadcrumb-item"><a href="{% url 'listings:listing_detail' pk=listing.pk %}">{{ listing.title }}</a></li>
                  <li class="breadcrumb-item active" aria-current="page">Book</li>
                </ol>
            </nav>

            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title mb-0">Book Your Experience: {{ listing.title }}</h2>
                </div>
                <div class="card-body p-4">
                    <p class="text-muted">Fill out the details below to request your booking.</p>
                    <p><strong>Vendor:</strong> <a href="{% url 'listings:vendor_detail' pk=listing.vendor.pk %}">{{ listing.vendor.name }}</a></p>
                    {% if listing.price %}
                        <p><strong>Price per person/item:</strong> ${{ listing.price }}</p>
                    {% else %}
                        <p><strong>Price:</strong> Contact vendor or check details</p>
                    {% endif %}
                    {% if listing.duration_minutes %}
                        <p><strong>Duration:</strong> {{ listing.duration_minutes }} minutes</p>
                    {% endif %}

                    <hr>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {# Display Non-Field Errors (if any) #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        {# Loop through form fields #}
                        {% for field in form %}
                            <div class="mb-3">
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>

                                {# Special handling for textarea (special_requests) #}
                                {% if field.name == 'special_requests' %}
                                    <textarea name="{{ field.name }}"
                                              id="{{ field.id_for_label }}"
                                              class="form-control {% if field.errors %}is-invalid{% endif %}"
                                              rows="3">{{ field.value|default:'' }}</textarea>
                                {# Handling for datetime-local input #}
                                {% elif field.name == 'booking_date' %}
                                     <input type="datetime-local"
                                           name="{{ field.name }}"
                                           id="{{ field.id_for_label }}"
                                           class="form-control {% if field.errors %}is-invalid{% endif %}"
                                           value="{{ field.value|date:'Y-m-d\TH:i'|default:'' }}" {# Format for datetime-local value #}
                                           required>
                                {# Default input rendering #}
                                {% else %}
                                    <input type="{{ field.field.widget.input_type }}"
                                           name="{{ field.name }}"
                                           id="{{ field.id_for_label }}"
                                           class="form-control {% if field.errors %}is-invalid{% endif %}"
                                           {% if field.field.widget.attrs.min %}min="{{ field.field.widget.attrs.min }}"{% endif %}
                                           {% if field.field.widget.attrs.value %}value="{{ field.field.widget.attrs.value }}"{% else %}value="{{ field.value|default:'' }}"{% endif %}
                                           {% if field.field.required %}required{% endif %}>
                                {% endif %}

                                {# Display Field-Specific Errors #}
                                {% if field.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in field.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}

                                {# Display Help Text #}
                                {% if field.help_text %}
                                    <div class="form-text text-muted"><small>{{ field.help_text|safe }}</small></div>
                                {% endif %}
                            </div>
                        {% endfor %}

                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-success btn-lg">Submit Booking Request</button>
                            <a href="{% url 'listings:listing_detail' pk=listing.pk %}" class="btn btn-outline-secondary">Cancel</a>
                        </div>
                    </form>
                </div> {# End card-body #}
            </div> {# End card #}
        </div> {# End col #}
    </div> {# End row #}
</div> {# End container #}
{% endblock %}

{% block extra_scripts %}
{# Optional: Add JS for date/time picker enhancements if needed #}
<script>
    // Basic validation to prevent selecting past dates with HTML5 picker
    document.addEventListener('DOMContentLoaded', function() {
        const dateInput = document.getElementById('{{ form.booking_date.id_for_label }}');
        if (dateInput) {
            const now = new Date();
            // Format date to YYYY-MM-DDTHH:MM required by datetime-local input
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const day = now.getDate().toString().padStart(2, '0');
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const minDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;

            dateInput.min = minDateTime;
        }
    });
</script>
{% endblock %}
