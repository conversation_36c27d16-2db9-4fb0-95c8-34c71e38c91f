# bookings/forms.py
from django import forms
from .models import Booking

class BookingForm(forms.ModelForm):
    # Use a specific widget for date/time selection
    booking_date = forms.DateTimeField(
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local'}),
        input_formats=['%Y-%m-%dT%H:%M'] # Match HTML5 datetime-local format
    )

    class Meta:
        model = Booking
        fields = ['booking_date', 'number_of_people', 'special_requests']
        widgets = {
            'special_requests': forms.Textarea(attrs={'rows': 3}),
            'number_of_people': forms.NumberInput(attrs={'min': '1', 'value': '1'})
        }
        labels = {
            'booking_date': 'Requested Date and Time',
            'number_of_people': 'Number of People',
            'special_requests': 'Special Requests (Optional)',
        }


# --- ADD THIS FORM ---
class DummyPaymentForm(forms.Form):
    # --- ADD THESE FIELDS ---
    cardholder_name = forms.Char<PERSON>ield(
        label="Full Name on Card",
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'e.g., <PERSON>'})
    )
    contact_number = forms.CharField(
        label="Contact Number",
        max_length=20, # Keep max length reasonable
        widget=forms.TextInput(attrs={'type': 'tel', 'class': 'form-control', 'placeholder': 'e.g., +1234567890'})
    )
    # --- END ADDED FIELDS ---
    #     
    card_number = forms.CharField(
        label="Card Number",
        max_length=19, # Max length for card numbers with spaces
        widget=forms.TextInput(attrs={'placeholder': 'xxxx xxxx xxxx xxxx', 'class': 'form-control'})
    )
    expiry_date = forms.CharField(
        label="Expiry (MM/YY)",
        max_length=5, # MM/YY
        widget=forms.TextInput(attrs={'placeholder': 'MM/YY', 'class': 'form-control'})
    )
    cvc = forms.CharField(
        label="CVC",
        max_length=4,
        widget=forms.TextInput(attrs={'placeholder': 'xxx', 'class': 'form-control'})
    )

    # Add required=True to the new fields
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['cardholder_name'].required = True
        self.fields['contact_number'].required = True
        self.fields['card_number'].required = True # Ensure card details are also required
        self.fields['expiry_date'].required = True
        self.fields['cvc'].required = True

        # Add visual required indicator to labels if not handled by template loop
        self.fields['cardholder_name'].label += " (Required)"
        self.fields['contact_number'].label += " (Required)"
        self.fields['card_number'].label += " (Required)"
        self.fields['expiry_date'].label += " (Required)"
        self.fields['cvc'].label += " (Required)"


    # Optional: Add custom validation
    def clean_card_number(self):
        card_number = self.cleaned_data.get('card_number')
        # Basic check: remove spaces and ensure it's digits (very basic)
        if card_number:
            card_number_digits = card_number.replace(" ", "")
            if not card_number_digits.isdigit():
                raise forms.ValidationError("Card number should only contain digits and spaces.")
            if not (13 <= len(card_number_digits) <= 16): # Common card lengths
                 raise forms.ValidationError("Invalid card number length.")
        return card_number # Return the original value with spaces for storage if desired

    def clean_expiry_date(self):
        expiry = self.cleaned_data.get('expiry_date')
        if expiry:
            if not (len(expiry) == 5 and expiry[2] == '/'):
                raise forms.ValidationError("Expiry date must be in MM/YY format.")
            try:
                month, year_short = expiry.split('/')
                month = int(month)
                year = int(f"20{year_short}") # Assuming 21st century
                if not (1 <= month <= 12):
                    raise forms.ValidationError("Invalid month.")
                # Add more sophisticated date checking if needed (e.g., not in past)
            except ValueError:
                raise forms.ValidationError("Invalid date format.")
        return expiry

    def clean_cvc(self):
        cvc = self.cleaned_data.get('cvc')
        if cvc and (not cvc.isdigit() or not (3 <= len(cvc) <= 4)):
            raise forms.ValidationError("Invalid CVC.")
        return cvc
# --- END OF ADDED FORM ---