# users/views.py
from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth import login
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test # <-- Import user_passes_test
from django.views.decorators.http import require_POST # <-- Import require_POST
from .forms import UserRegisterForm, ProfileForm,UserAndProfileEditForm  # Create these forms
from .models import Profile # Import the Profile model
from listings.models import Vendor, Review,FavoriteListing, FavoriteVendor # To check if user is a vendor owner
from bookings.models import Booking # To show user's bookings
# --- Add these imports ---
from django.contrib.auth.views import LoginView
from django.conf import settings # To access session settings
from django.http import Http404 # For get_object_or_404
# --- End imports ---
# --- Helper function for staff check ---
def staff_check(user):
    return user.is_staff
# --- End Helper ---
def register(request):
    if request.user.is_authenticated: # Prevent logged-in users from re-registering
        return redirect('listings:home')
    
    if request.method == 'POST':
        form = UserRegisterForm(request.POST, request.FILES)
        if form.is_valid():
            print("Form is valid.")
            print(f"Form cleaned data: {form.cleaned_data}") # See all cleaned data
            phone_number_from_form = form.cleaned_data.get('phone_number')
            print(f"Phone number from form: {phone_number_from_form}")

            user = form.save()
            print(f"User created: {user.username}")

            profile, profile_created = Profile.objects.get_or_create(user=user)
            print(f"Profile fetched/created: {profile}, Created: {profile_created}")
                        # --- SAVE PHONE NUMBER TO PROFILE ---
            # Get or create the profile for the new user
            # The Profile model should have a signal to auto-create on User creation,
            # or you handle it here. get_or_create is robust.

            if phone_number_from_form:
                profile.phone_number = phone_number_from_form
                print(f"Attempting to save phone number '{profile.phone_number}' to profile.")
                profile.save()
                
                # Verify immediately from DB
                try:
                    updated_profile = Profile.objects.get(user=user)
                    print(f"Profile phone from DB after save: {updated_profile.phone_number}")
                except Profile.DoesNotExist:
                    print("Profile not found in DB after save attempt.")
            else:
                print("No phone number provided in form to save.")
            # Handle avatar upload
            avatar_file = form.cleaned_data.get('avatar')
            if avatar_file:
                profile.avatar = avatar_file
                print(f"Attempting to save avatar '{avatar_file.name}' to profile.")
            
            profile.save() # Save profile with phone number and potentially avatar
            # Verify avatar from DB if needed
            updated_profile_for_avatar = Profile.objects.get(user=user)
            print(f"Profile avatar from DB after save: {updated_profile_for_avatar.avatar}")


            
            # Optionally set user type during registration or in profile edit
            # user.profile.user_type = request.POST.get('user_type', 'tourist') # Example
            # user.profile.save()
            login(request, user) # Log the user in directly
            messages.success(request, f'Account created successfully for {user.username}! Your details have been saved.')
            return redirect('listings:home') # Redirect to homepage
        else:
            print("Form is NOT valid.")
            print(f"Form errors: {form.errors}") # See validation errors
            messages.error(request, "Registration failed. Please correct the errors below.")
    else:
        form = UserRegisterForm()
    return render(request, 'registration/register.html', {'form': form})

@login_required
def profile(request):
    user = request.user
    # Ensure profile exists, create if not (should be handled by signal, but good for robustness)
    current_user_profile, created = Profile.objects.get_or_create(user=user)

    if request.method == 'POST':
        # Pass both user and profile instances to the form
        form = UserAndProfileEditForm(request.POST, request.FILES, instance=user, instance_profile=current_user_profile)
        if form.is_valid():
            form.save() # The form's save method handles saving to both User and Profile
            messages.success(request, 'Your profile has been updated successfully.')
            return redirect('users:profile')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        # Pass both instances for initial data population
        form = UserAndProfileEditForm(instance=user, instance_profile=current_user_profile)

    # --- Context data for other sections of the profile page ---
    user_bookings = Booking.objects.filter(user=user).select_related('listing', 'listing__vendor').order_by('-booking_date')
    user_reviews = Review.objects.filter(user=user).select_related('listing').order_by('-created_at')
    is_vendor_owner = Vendor.objects.filter(owner=user).exists()
    user_favorite_listings = FavoriteListing.objects.filter(user=user).select_related('listing', 'listing__vendor')
    user_favorite_vendors = FavoriteVendor.objects.filter(user=user).select_related('vendor')
    # --- End context data ---

    context = {
        'profile_form': form, # Use 'profile_form' to match the template
        'user_bookings': user_bookings,
        'user_reviews': user_reviews,
        'is_vendor_owner': is_vendor_owner,
        'user_favorite_listings': user_favorite_listings,
        'user_favorite_vendors': user_favorite_vendors,
    }

    if user.is_staff:
        all_vendors = Vendor.objects.all().select_related('owner').order_by('is_approved', '-created_at')
        context['all_vendors_for_staff'] = all_vendors
        all_user_profiles = Profile.objects.all().select_related('user').exclude(user=user).order_by('user__username')
        context['all_user_profiles_for_staff'] = all_user_profiles

    return render(request, 'users/profile.html', context)
# --- ADD STAFF USER TYPE MANAGEMENT VIEW ---
@login_required
@user_passes_test(staff_check)
@require_POST
def staff_set_user_type(request, profile_pk):
    target_profile = get_object_or_404(Profile, pk=profile_pk)
    new_type = request.POST.get('user_type') # Get new type from form submission

    # Validate new_type against choices
    valid_types = [choice[0] for choice in Profile.USER_TYPE_CHOICES]
    if new_type not in valid_types:
        messages.error(request, "Invalid user type selected.")
        return redirect('users:profile')

    if target_profile.user == request.user:
        messages.error(request, "You cannot change your own user type using this interface.")
        return redirect('users:profile')

    old_type_display = target_profile.get_user_type_display()
    target_profile.user_type = new_type
    target_profile.save()
    new_type_display = target_profile.get_user_type_display()

    messages.success(request, f"User '{target_profile.user.username}' type changed from '{old_type_display}' to '{new_type_display}'.")
    return redirect('users:profile')
# --- END OF STAFF USER TYPE MANAGEMENT VIEW ---


# --- Add this Custom Login View ---
class CustomLoginView(LoginView):
    template_name = 'registration/login.html' # Explicitly set template

    def form_valid(self, form):
        """Security check complete. Log the user in."""
        # Perform the actual login
        login(self.request, form.get_user())

        # Check if 'Remember me' was checked
        remember_me = self.request.POST.get('remember_me')

        if not remember_me:
            # If "Remember me" is NOT checked, set session expiry to 0 (expires on browser close)
            self.request.session.set_expiry(0)
            # Make the session cookie expire explicitily at browser close
            self.request.session.modified = True
        else:
            # If "Remember me" IS checked, use the default session age (SESSION_COOKIE_AGE)
            # Setting expiry to None uses the global setting (default: 2 weeks)
            self.request.session.set_expiry(settings.SESSION_COOKIE_AGE)


        # Proceed with the default LoginView redirect behavior
        return super().form_valid(form)

# --- End Custom Login View ---