# users/urls.py
from django.urls import path
from . import views

app_name = 'users'

urlpatterns = [
    path('register/', views.register, name='register'),
    path('profile/', views.profile, name='profile'),
    # Add other profile-related URLs (edit profile, etc.)
    # --- ADD STAFF USER TYPE MANAGEMENT URL ---
    path('staff/set_user_type/<int:profile_pk>/', views.staff_set_user_type, name='staff_set_user_type'),
    # --- END OF STAFF URL ---

]
