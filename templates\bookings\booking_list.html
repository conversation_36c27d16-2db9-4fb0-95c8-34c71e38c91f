{% extends "base.html" %}
{% load static %}

{% block title %}{{ page_title|default:"Bookings" }} - TasteLocal{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">{{ page_title|default:"Bookings" }}</h1>

    {% if bookings %}
        <div class="card shadow-sm">
            <div class="card-header">
                <h5 class="mb-0">Your Upcoming and Past Bookings</h5>
            </div>
            <ul class="list-group list-group-flush">
                {% for booking in bookings %}
                <li class="list-group-item p-3">
                    <div class="row align-items-center">
                        <div class="col-md-6 mb-2 mb-md-0">
                            <h6 class="mb-1">
                                <a href="{% url 'listings:listing_detail' pk=booking.listing.pk %}" class="text-decoration-none fw-bold">{{ booking.listing.title }}</a>
                            </h6>
                            <small class="text-muted">
                                By: <a href="{% url 'listings:vendor_detail' pk=booking.listing.vendor.pk %}" class="text-decoration-none text-muted">{{ booking.listing.vendor.name }}</a>
                            </small>
                        </div>
                        <div class="col-md-4 mb-2 mb-md-0">
                            <small class="d-block"><strong>Date:</strong> {{ booking.booking_date|date:"D, M j, Y, P" }}</small>
                            <small class="d-block"><strong>Guests:</strong> {{ booking.number_of_people }}</small>
                        </div>
                        <div class="col-md-2 text-md-end">
                            <span class="badge rounded-pill d-block d-md-inline-block mb-1
                                {% if booking.status == 'confirmed' %} bg-success
                                {% elif booking.status == 'pending' %} bg-warning text-dark
                                {% elif booking.status == 'cancelled' %} bg-danger
                                {% elif booking.status == 'completed' %} bg-secondary
                                {% else %} bg-light text-dark {% endif %}">
                                {{ booking.get_status_display }}
                            </span>
                            <a href="{% url 'bookings:booking_detail' pk=booking.pk %}" class="btn btn-sm btn-outline-primary mt-1 mt-md-0">View Details</a>
                        </div>
                    </div>
                </li>
                {% endfor %}
            </ul>
        </div>
    {% else %}
        <div class="alert alert-info text-center" role="alert">
            You have no bookings yet. Ready to explore?
        </div>
        <div class="text-center">
             <a href="{% url 'listings:listing_list' %}" class="btn btn-primary">Find Experiences</a>
        </div>
    {% endif %}

</div> {# End container #}
{% endblock %}
