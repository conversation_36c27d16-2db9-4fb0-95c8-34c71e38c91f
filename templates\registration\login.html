{% extends "base.html" %}
{% load static %}

{% block title %}Log In - TasteLocal{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title mb-0 text-center">Log In to TasteLocal</h2>
                </div>
                <div class="card-body p-4">
                    <p class="text-muted text-center mb-4">Access your profile, bookings, and reviews.</p>

                    <form method="post" action="{% url 'login' %}" novalidate>
                        {% csrf_token %}

                        {# Display Non-Field Errors (e.g., invalid credentials) #}
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        {# Username Field #}
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">{{ form.username.label }}</label>
                            <input type="text"
                                   name="{{ form.username.name }}"
                                   id="{{ form.username.id_for_label }}"
                                   class="form-control {% if form.username.errors %}is-invalid{% endif %}"
                                   required
                                   autofocus
                                   value="{{ form.username.value|default:'' }}">
                            {% if form.username.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {# Password Field #}
                        <div class="mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label">{{ form.password.label }}</label>
                            <input type="password"
                                   name="{{ form.password.name }}"
                                   id="{{ form.password.id_for_label }}"
                                   class="form-control {% if form.password.errors %}is-invalid{% endif %}"
                                   required>
                             {% if form.password.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.password.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>



                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">Log In</button>
                        </div>

                        {# Hidden field for redirection after login (Django handles this automatically often, but can be explicit) #}
                        <input type="hidden" name="next" value="{{ next }}">

                    </form>

                    <hr class="my-4">

                    <div class="text-center">
                        <p class="mb-1"><a href="{% url 'password_reset' %}">Forgot Password?</a></p>
                        <p class="mb-0">Don't have an account? <a href="{% url 'users:register' %}">Register Here</a></p>
                    </div>
                </div> {# End card-body #}
            </div> {# End card #}
        </div> {# End col #}
    </div> {# End row #}
</div> {# End container #}
{% endblock %}
