{% extends "base.html" %}
{% load static %}

{% block title %}Dummy Payment - {{ booking.listing.title }}{% endblock %}

{% block content %}
<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb">
                  <li class="breadcrumb-item"><a href="{% url 'listings:home' %}">Home</a></li>
                  <li class="breadcrumb-item"><a href="{% url 'listings:listing_detail' pk=booking.listing.pk %}">{{ booking.listing.title }}</a></li>
                  <li class="breadcrumb-item active" aria-current="page">Payment</li>
                </ol>
            </nav>

            <div class="card shadow-lg">
                <div class="card-header bg-success text-white">
                    <h2 class="card-title mb-0 text-center"><i class="fas fa-credit-card me-2"></i>Dummy Payment Gateway</h2>
                </div>
                <div class="card-body p-4">
                    <h4 class="text-center mb-3">Booking Summary</h4>
                    <ul class="list-group list-group-flush mb-4">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Listing:</strong>
                            <span>{{ booking.listing.title }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Vendor:</strong>
                            <span>{{ booking.listing.vendor.name }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Date & Time:</strong>
                            <span>{{ booking.booking_date|date:"D, M j, Y, P" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Guests:</strong>
                            <span>{{ booking.number_of_people }}</span>
                        </li>
                        {% if booking.listing.price %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Price per Guest/Item:</strong>
                            <span>${{ booking.listing.price|floatformat:2 }}</span>
                        </li>
                        {% endif %}
                        <li class="list-group-item d-flex justify-content-between align-items-center bg-light">
                            <strong class="fs-5 text-success">Total Amount Due:</strong>
                            <span class="fs-5 fw-bold text-success">${{ amount_to_pay|floatformat:2 }}</span>
                        </li>
                    </ul>

                    <p class="text-muted text-center small">
                        This is a simulated payment page. No real transaction will occur.
                    </p>

                    <form method="post">
                        {% csrf_token %}

                        {# Display Non-Field Errors from payment_form #}
                        {% if payment_form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in payment_form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        {# --- ADD NEW FIELDS --- #}
                        {# Cardholder Name #}
                        <div class="mb-3">
                            {% with field=payment_form.cardholder_name %}
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                {{ field.as_widget }}
                                {% if field.errors %}
                                    <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                {% endif %}
                            {% endwith %}
                        </div>
                        {# Contact Number #}
                        <div class="mb-3">
                            {% with field=payment_form.contact_number %}
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                {{ field.as_widget }}
                                {% if field.errors %}
                                    <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                {% endif %}
                            {% endwith %}
                        </div>
                        {# --- END NEW FIELDS --- #}


                        {# Card Number #}
                        <div class="mb-3">
                            {% with field=payment_form.card_number %}
                                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                <div class="input-group"> {# Use input group for logo #}
                                    {{ field.as_widget }}
                                    {# Placeholder for the card logo #}
                                    <span class="input-group-text bg-light" id="card-logo-placeholder" style="width: 40px; padding: 0; justify-content: center;">
                                        {# Logo will be added here by JS #}
                                    </span>
                                    {% if field.errors %}
                                        {# Invalid feedback needs to be outside input-group for proper display #}
                                        {# Or use has-validation class on input-group and put feedback inside #}
                                    {% endif %}
                                </div>
                                {% if field.errors %}
                                    <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                {% endif %}
                            {% endwith %}
                        </div>

                        <div class="row">
                            {# Expiry Date #}
                            <div class="col-md-6 mb-3">
                                {% with field=payment_form.expiry_date %}
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    {{ field.as_widget }}
                                    {% if field.errors %}
                                        <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                            {# CVC #}
                            <div class="col-md-6 mb-3">
                                {% with field=payment_form.cvc %}
                                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                                    {{ field.as_widget }}
                                    {% if field.errors %}
                                        <div class="invalid-feedback d-block">{{ field.errors|striptags }}</div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                        </div>

                        <div class="d-grid mt-4">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-lock me-2"></i>Pay ${{ amount_to_pay|floatformat:2 }} Now
                            </button>
                        </div>
                    </form>
                    <div class="text-center mt-3">
                        <a href="{% url 'bookings:booking_detail' pk=booking.pk %}" class="btn btn-sm btn-outline-secondary">Cancel Payment & View Booking</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const cardNumberInput = document.getElementById('{{ payment_form.card_number.id_for_label }}');
    const cardLogoPlaceholder = document.getElementById('card-logo-placeholder');

    if (cardNumberInput && cardLogoPlaceholder) {
        cardNumberInput.addEventListener('input', function() {
            const number = this.value.replace(/\s/g, ''); // Remove spaces

            let cardType = 'unknown';
            let logoSrc = '';

            // Basic card type detection based on prefixes
            if (number.startsWith('4')) {
                cardType = 'visa';
                logoSrc = '{% static "images/card_logos/visa.png" %}'; // Update path as needed
            } else if (number.startsWith('51') || number.startsWith('52') || number.startsWith('53') || number.startsWith('54') || number.startsWith('55')) {
                cardType = 'mastercard';
                logoSrc = '{% static "images/card_logos/mastercard.png" %}'; // Update path as needed
            } else if (number.startsWith('34') || number.startsWith('37')) {
                cardType = 'amex';
                logoSrc = '{% static "images/card_logos/amex.png" %}'; // Update path as needed
            }
            // Add more card types (Discover, Diners Club, etc.) as needed

            // Update the logo placeholder
            if (logoSrc) {
                cardLogoPlaceholder.innerHTML = `<img src="${logoSrc}" alt="${cardType} logo" style="height: 24px;">`; // Adjust height as needed
            } else {
                cardLogoPlaceholder.innerHTML = ''; // Clear if no type detected
            }
        });
    }
});
</script>
{% endblock %}

{# Optional: Add CSS for logos if using background images instead of img tags #}
{# {% block extra_head %}
<style>
    .card-logo {
        width: 40px; /* Match input-group-text width */
        height: 24px; /* Adjust height */
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
    .card-logo.visa { background-image: url('{% static "images/card_logos/visa.png" %}'); }
    .card-logo.mastercard { background-image: url('{% static "images/card_logos/mastercard.png" %}'); }
    .card-logo.amex { background-image: url('{% static "images/card_logos/amex.png" %}'); }
    /* Add more classes for other card types */
</style>
{% endblock %} #}
