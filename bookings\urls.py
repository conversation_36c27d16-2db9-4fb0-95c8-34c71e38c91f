# bookings/urls.py
from django.urls import path
from . import views

app_name = 'bookings'

urlpatterns = [
    path('create/<int:listing_id>/', views.create_booking, name='create_booking'),
    path('<int:pk>/', views.BookingDetailView.as_view(), name='booking_detail'),
    path('my_bookings/', views.user_bookings, name='user_bookings'),
    path('<int:pk>/cancel/', views.cancel_booking, name='cancel_booking'),
    path('<int:pk>/confirm/', views.confirm_booking, name='confirm_booking'),
    path('<int:pk>/reject/', views.reject_booking, name='reject_booking'),

    # --- ADD DUMMY PAYMENT URL ---
    path('payment/<int:booking_id>/', views.dummy_payment_view, name='dummy_payment'),
    # --- END DUMMY PAYMENT URL ---
]
